<?php

namespace Tests\Feature\Console\Commands;

use App\Console\Commands\AutoSweep;
use App\Enums\LoanAccountType;
use App\Jobs\AutoSweepLoanJob;
use App\Models\Loan;
use App\Models\LoanProduct;
use App\Models\Partner;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class AutoSweepTest extends TestCase
{
    use RefreshDatabase;

    private Partner $partner;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a partner for all tests
        $this->partner = Partner::factory()->create();
    }

    public function test_command_dispatches_jobs_for_eligible_loans()
    {
        Queue::fake();

        // Create loan product with auto sweep provider
        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create eligible loan (past maturity, no auto sweep started, not paid/written off)
        Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5), // Past maturity
            'Auto_Sweep_Started' => null, // Not started
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value // Not paid/written off
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_loans_without_auto_sweep_provider()
    {
        Queue::fake();

        // Create loan product without auto sweep provider
        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => null
        ]);

        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_unsupported_auto_sweep_provider()
    {
        Queue::fake();

        // Create loan product with unsupported auto sweep provider
        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'UnsupportedProvider'
        ]);

        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_loans_not_past_maturity()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create loan not past maturity date
        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->addDays(5), // Future maturity
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_loans_with_auto_sweep_already_started()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create loan with auto sweep already started
        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => Carbon::now()->subDays(2), // Already started
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_paid_off_loans()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create paid off loan
        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::PaidOff->value // Paid off
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_does_not_dispatch_jobs_for_written_off_loans()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create written off loan
        $loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::WrittenOff->value // Written off
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertNotPushed(AutoSweepLoanJob::class);
    }

    public function test_command_supports_nextpe_provider()
    {
        Queue::fake();

        // Create loan product with NextPe provider
        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'NextPe'
        ]);

        $eligibleLoan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertPushed(AutoSweepLoanJob::class, function ($job) use ($eligibleLoan) {
            // Use reflection to access the protected loanId property
            $reflection = new \ReflectionClass($job);
            $property = $reflection->getProperty('loanId');
            $property->setAccessible(true);
            return $property->getValue($job) === $eligibleLoan->id;
        });
    }

    public function test_command_dispatches_multiple_jobs_for_multiple_eligible_loans()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create multiple eligible loans
        $eligibleLoan1 = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $eligibleLoan2 = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(3),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::WithinTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        Queue::assertPushed(AutoSweepLoanJob::class, 2);
    }

    public function test_command_handles_mixed_eligible_and_ineligible_loans()
    {
        Queue::fake();

        $loanProductWithProvider = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        $loanProductWithoutProvider = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => null
        ]);

        // Create one eligible loan
        $eligibleLoan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProductWithProvider->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        // Create multiple ineligible loans
        $ineligibleLoan1 = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProductWithoutProvider->id, // No provider
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $ineligibleLoan2 = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProductWithProvider->id,
            'Maturity_Date' => Carbon::now()->addDays(5), // Future maturity
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $ineligibleLoan3 = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProductWithProvider->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::PaidOff->value // Paid off
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        // Only one job should be dispatched for the eligible loan
        Queue::assertPushed(AutoSweepLoanJob::class, function ($job) use ($eligibleLoan) {
            // Use reflection to access the protected loanId property
            $reflection = new \ReflectionClass($job);
            $property = $reflection->getProperty('loanId');
            $property->setAccessible(true);
            return $property->getValue($job) === $eligibleLoan->id;
        });

        Queue::assertPushed(AutoSweepLoanJob::class, 1);
    }

    public function test_command_returns_success_code_when_no_eligible_loans_found()
    {
        Queue::fake();

        // Don't create any loans - command should still succeed with exit code 0
        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        // No jobs should be dispatched
        Queue::assertNothingPushed();
    }

    public function test_command_uses_lazy_loading_for_performance()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create multiple eligible loans to test lazy loading
        for ($i = 0; $i < 5; $i++) {
            Loan::factory()->create([
                'Partner_ID' => $this->partner->id,
                'Loan_Product_ID' => $loanProduct->id,
                'Maturity_Date' => Carbon::now()->subDays(5),
                'Auto_Sweep_Started' => null,
                'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
            ]);
        }

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        // Verify that jobs were dispatched for all eligible loans
        Queue::assertPushed(AutoSweepLoanJob::class, 5);
    }

    public function test_command_only_selects_required_fields()
    {
        Queue::fake();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        $eligibleLoan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        // The command should work correctly with only id and loan_product_id selected
        Queue::assertPushed(AutoSweepLoanJob::class, function ($job) use ($eligibleLoan) {
            // Use reflection to access the protected loanId property
            $reflection = new \ReflectionClass($job);
            $property = $reflection->getProperty('loanId');
            $property->setAccessible(true);
            return $property->getValue($job) === $eligibleLoan->id;
        });
    }

    public function test_command_processes_loans_from_all_partners()
    {
        Queue::fake();

        // Create another partner
        $otherPartner = Partner::factory()->create();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        $otherPartnerLoanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $otherPartner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        // Create eligible loan for first partner
        $firstPartnerLoan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        // Create eligible loan for second partner (console commands process all partners)
        $secondPartnerLoan = Loan::factory()->create([
            'Partner_ID' => $otherPartner->id,
            'Loan_Product_ID' => $otherPartnerLoanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5),
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);

        $this->artisan('lms:auto-sweep')
            ->assertExitCode(0);

        // Both loans should be processed since console commands don't have partner scope
        Queue::assertPushed(AutoSweepLoanJob::class, function ($job) use ($firstPartnerLoan, $secondPartnerLoan) {
            // Use reflection to access the protected loanId property
            $reflection = new \ReflectionClass($job);
            $property = $reflection->getProperty('loanId');
            $property->setAccessible(true);
            $loanId = $property->getValue($job);
            return $loanId === $firstPartnerLoan->id || $loanId === $secondPartnerLoan->id;
        });

        Queue::assertPushed(AutoSweepLoanJob::class, 2);
    }
}
