<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CsrfTokenRefreshTest extends TestCase
{
    /**
     * Test that the CSRF token refresh endpoint works correctly.
     *
     * @return void
     */
    public function test_csrf_token_refresh_endpoint_returns_valid_response()
    {
        $response = $this->get('/refresh-csrf-token');

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'csrf_token',
                     'success',
                     'timestamp'
                 ])
                 ->assertJson([
                     'success' => true
                 ]);

        // Verify that the token is not empty
        $data = $response->json();
        $this->assertNotEmpty($data['csrf_token']);
        $this->assertTrue($data['success']);
    }

    /**
     * Test that the CSRF token refresh endpoint returns a valid token format.
     *
     * @return void
     */
    public function test_csrf_token_refresh_returns_valid_token_format()
    {
        $response = $this->get('/refresh-csrf-token');
        
        $data = $response->json();
        $token = $data['csrf_token'];
        
        // CSRF tokens should be 40 characters long
        $this->assertEquals(40, strlen($token));
        
        // Should contain only alphanumeric characters
        $this->assertMatchesRegularExpression('/^[a-zA-Z0-9]+$/', $token);
    }

    /**
     * Test that multiple calls to refresh endpoint return consistent tokens within same session.
     *
     * @return void
     */
    public function test_multiple_csrf_refresh_calls_return_consistent_tokens_in_same_session()
    {
        $response1 = $this->get('/refresh-csrf-token');
        $response2 = $this->get('/refresh-csrf-token');

        $token1 = $response1->json('csrf_token');
        $token2 = $response2->json('csrf_token');

        // Tokens should be the same within the same session (this is correct Laravel behavior)
        $this->assertEquals($token1, $token2);
    }

    /**
     * Test that the endpoint accepts AJAX requests properly.
     *
     * @return void
     */
    public function test_csrf_refresh_accepts_ajax_requests()
    {
        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json'
        ])->get('/refresh-csrf-token');

        $response->assertStatus(200)
                 ->assertHeader('Content-Type', 'application/json');
    }
}
