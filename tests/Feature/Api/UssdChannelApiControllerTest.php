<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Partner;
use App\Models\Customer;
use App\Models\CreditScore;
use App\Models\LoanProduct;
use App\Models\Transaction;
use App\Models\LoanApplication;
use App\Models\LoanProductTerm;
use App\Models\PartnerApiSetting;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class UssdChannelApiControllerTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    private $phone_number = '256789123456';

    // @getLastTransaction
    public function test_it_can_get_last_transaction()
    {
        // Given ...
        $partner = Partner::factory()->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $transaction = Transaction::factory([
            'Partner_ID' => $partner->id,
            'Telephone_Number' => $this->phone_number,
            'Status' => 'Completed'
        ])->create();

        // When ...
        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/getLastTransaction', [
                'phoneNumber' => $this->phone_number,
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'transaction' => [
                    'id' => $transaction->id,
                    'Partner_ID' => $partner->id,
                    'Type' => $transaction->Type
                ],
            ],
        ]);
    }

    public function test_it_does_not_get_transaction_when_phone_number_is_invalid()
    {
        // Given ...
        $partner = Partner::factory()->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // When ...
        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/getLastTransaction', [
                'phoneNumber' => '25678912345',
            ]
        );

        // Then ...
        $response->assertJson([
            'returnCode' => 21,
            'returnMessage' => 'An unexpected error occurred',
            'error' => 'The phone number must be exactly 12 digits.'
        ]);
    }

    // @getCustomerDetails
    public function test_it_can_get_customer_details()
    {
        // Given ...
        config([
            'services.mtn.test.dfcu_ca_certificate' => '',
            'services.mtn.test.dfcu_certificate' => '',
            'services.mtn.test.lms_private_key' => '',
            'services.mtn.test.m3_external_certificate' => '',
            'services.mtn.test.loans_username' => '',
            'services.mtn.test.loans_password' => '',
            'services.mtn.test.savings_username' => '',
            'services.mtn.test.savings_password' => '',
        ]);

        $partner = Partner::factory()->create();
        $customer = Customer::factory([
            'Telephone_Number' => $this->phone_number
        ])->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // When ...
        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/getCustomerDetails', [
                'phoneNumber' => $this->phone_number,
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'customer' => [
                    'customerName' => $customer->name,
                    'customerid' => $customer->Telephone_Number
                ],
            ],
        ]);
    }

    public function test_it_does_not_get_customer_details_when_phone_number_is_invalid()
    {
        // Given ...
        config([
            'services.mtn.test.dfcu_ca_certificate' => '',
            'services.mtn.test.dfcu_certificate' => '',
            'services.mtn.test.lms_private_key' => '',
            'services.mtn.test.m3_external_certificate' => '',
            'services.mtn.test.loans_username' => '',
            'services.mtn.test.loans_password' => '',
            'services.mtn.test.savings_username' => '',
            'services.mtn.test.savings_password' => '',
        ]);

        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // When ...
        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/getCustomerDetails', [
                'phoneNumber' => '078912345',
            ]
        );

        // Then ...
        $response->assertJson([
            'returnCode' => 21,
            'returnMessage' => 'An unexpected error occurred',
            'error' => 'The phone number must be exactly 12 digits.'
        ]);
    }

    // @postCreditScore
    public function test_it_can_post_credit_score()
    {
        // Given ...
        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $customer = Customer::factory([
            'Telephone_Number' => $this->phone_number
        ])->create();
        $mno_score = 10;

        // When ...
        $response = $this
            ->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/postCreditScore', [
                'phoneNumber' => $this->phone_number,
                'requestId' => 'lorem-ipsum',
                'overrideLimit' => false,
                'creditLimit' => 0,
                'mnoScore' => $mno_score
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'phone' => $customer->Telephone_Number,
                'requestId' => 'lorem-ipsum',
                "scores" => [
                    "MNO" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                    "CRB" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                ],
            ],
        ]);

        $credit_score = CreditScore::latest('id')->first();
        $this->assertEquals(
            $customer->id,
            $credit_score->customerId
        );
        $this->assertEquals(
            $mno_score,
            $credit_score->mnoScore
        );
    }

    public function test_it_does_not_create_credit_score_when_mno_is_not_given()
    {
        // Given ...
        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $customer = Customer::factory([
            'Telephone_Number' => $this->phone_number
        ])->create();

        // When ...
        $response = $this
            ->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/postCreditScore', [
                'phoneNumber' => $this->phone_number,
                'requestId' => 'lorem-ipsum',
                'overrideLimit' => false,
                'creditLimit' => 0,
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'phone' => $customer->Telephone_Number,
                'requestId' => 'lorem-ipsum',
                "scores" => [
                    "MNO" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                    "CRB" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                ],
            ],
        ]);

        $this->assertNull(CreditScore::where('customerId', $customer->id)->first());
    }

    public function test_it_creates_customer_if_none_with_given_phone_number_exists()
    {
        // Given ...
        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $this->assertNull(Customer::where('Telephone_Number', $this->phone_number)->first());

        // When ...
        $response = $this
            ->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/postCreditScore', [
                'phoneNumber' => $this->phone_number,
                'requestId' => 'lorem-ipsum',
                'overrideLimit' => false,
                'creditLimit' => 0,
                'firstName' => $this->faker->firstName(),
                'lastName' => $this->faker->lastName(),
                'gender' => 'Male',
                'dateOfBirth' => $this->faker->date(),
                'idType' => 'Passport Number',
                'idNumber' => $this->faker->ean13(),
                'classification' => 'Individual',
                'emailAddress' => $this->faker->unique()->safeEmail(),
                'maritalStatus' => 'Married'
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'phone' => $this->phone_number,
                'requestId' => 'lorem-ipsum',
                "scores" => [
                    "MNO" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                    "CRB" => [
                        "score" => '857',
                        "band" => 'AAA',
                        "rating" => 'Excellent',
                    ],
                ],
            ],
        ]);

        $this->assertNotNull(Customer::where('Telephone_Number', $this->phone_number)->first());
    }

    public function test_it_throws_validation_error_when_phone_number_is_invalid()
    {
        // Given ...
        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // When ...
        $response = $this
            ->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/postCreditScore', [
                'phoneNumber' => '25678912345',
                'requestId' => 'lorem-ipsum',
                'overrideLimit' => false,
                'creditLimit' => 0,
            ]
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 1,
            'returnMessage' => 'Validation error',
            'errors' => [
                'phoneNumber' => [
                    'The phone number must be exactly 12 digits.'
                ]
            ]
        ]);
    }

    // @postLoanApplication
    public function test_it_can_post_loan_application()
    {
        // Given ...
        $partner = Partner::factory()->create();
        $customer = Customer::factory([
            'Telephone_Number' => $this->phone_number
        ])->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $loan_product = LoanProduct::factory([
            'Partner_ID' => $partner->id,
        ])->createQuietly();
        $loan_product_terms = LoanProductTerm::factory([
            'Partner_ID' => $partner->id,
            'Loan_Product_ID' => $loan_product->id
        ])->create();

        $form_data = [
            'phoneNumber' => $this->phone_number,
            'requestId' => 'lorem-ipsum',
            'loanAmount' => 10000,
            'loanProductCode' => $loan_product->Code,
            'loanProductTermCode' => $loan_product_terms->Code,
            'loanPurpose' => 'School Fees',
            'frequencyOfInstallmentRepayment' => 'Monthly',
            'loanTermInDays' => 4
        ];

        // When ...
        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/ussd/postLoanApplication', $form_data
        );

        // Then ...
        $response->assertOk();
        $response->assertJson([
            'returnCode' => 0,
            'returnData' => [
                'interestRate' => "{$loan_product_terms->Interest_Rate}%",
                'interestCycle' => 'Monthly'
            ],
        ]);

        $loan_application = LoanApplication::latest('id')->first();
        $this->assertEquals(
            $customer->id,
            $loan_application->Customer_ID
        );
        $this->assertEquals(
            $form_data['loanAmount'],
            $loan_application->Credit_Amount_Approved
        );
    }
}
