<?php

namespace Tests\Unit\Jobs;

use App\Actions\Loans\AutoSweepLoan;
use App\Jobs\AutoSweepLoanJob;
use App\Models\Loan;
use App\Models\LoanProduct;
use App\Models\Partner;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class AutoSweepLoanJobTest extends TestCase
{
    use RefreshDatabase;

    private Loan $loan;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $partner = Partner::factory()->create();

        $loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        $this->loan = Loan::factory()->create([
            'Partner_ID' => $partner->id,
            'Loan_Product_ID' => $loanProduct->id,
        ]);
    }

    public function test_job_calls_auto_sweep_loan_action_with_correct_loan()
    {
        // Mock the AutoSweepLoan action
        $mockAction = Mockery::mock(AutoSweepLoan::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(Mockery::on(function ($loan) {
                return $loan instanceof Loan && $loan->id === $this->loan->id;
            }))
            ->andReturn(true);

        // Bind the mock to the container
        $this->app->instance(AutoSweepLoan::class, $mockAction);

        // Create and handle the job
        $job = new AutoSweepLoanJob($this->loan->id);
        $job->handle();

        // Verify that the mock expectations were met
        $this->assertTrue(true); // This will pass if Mockery expectations are satisfied
    }

    public function test_job_fails_when_loan_not_found()
    {
        $nonExistentLoanId = 99999;

        // Mock Log to capture the error
        Log::shouldReceive('error')
            ->once()
            ->withArgs(function ($message, $context) use ($nonExistentLoanId) {
                return str_contains($message, "Auto sweep job failed for loan #{$nonExistentLoanId}") &&
                       str_contains($message, "No query results for model") &&
                       $context['loan_id'] === $nonExistentLoanId &&
                       str_contains($context['error'], "No query results for model");
            });

        // Create job with non-existent loan ID
        $job = new AutoSweepLoanJob($nonExistentLoanId);

        // The job should handle the exception internally
        $job->handle();

        // Verify the log was called (Mockery will check this automatically)
        $this->assertTrue(true);
    }

    public function test_job_handles_action_exception_and_logs_error()
    {
        $exceptionMessage = 'Auto sweep action failed';

        // Mock the AutoSweepLoan action to throw an exception
        $mockAction = Mockery::mock(AutoSweepLoan::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Loan::class))
            ->andThrow(new Exception($exceptionMessage));

        $this->app->instance(AutoSweepLoan::class, $mockAction);

        // Mock Log to capture the error
        Log::shouldReceive('error')
            ->once()
            ->withArgs(function ($message, $context) use ($exceptionMessage) {
                return $message === "Auto sweep job failed for loan #{$this->loan->id}: {$exceptionMessage}" &&
                       $context['loan_id'] === $this->loan->id &&
                       $context['error'] === $exceptionMessage;
            });

        $job = new AutoSweepLoanJob($this->loan->id);
        $job->handle();

        // Verify the mock expectations were met
        $this->assertTrue(true);
    }

    public function test_job_completes_successfully_when_action_succeeds()
    {
        // Mock the AutoSweepLoan action to return true (success)
        $mockAction = Mockery::mock(AutoSweepLoan::class);
        $mockAction->shouldReceive('execute')
            ->once()
            ->with(Mockery::type(Loan::class))
            ->andReturn(true);

        $this->app->instance(AutoSweepLoan::class, $mockAction);

        // Ensure no error logging occurs
        Log::shouldReceive('error')->never();

        // Create and handle the job
        $job = new AutoSweepLoanJob($this->loan->id);
        $job->handle();

        // If we reach this point without exceptions, the test passes
        $this->assertTrue(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
