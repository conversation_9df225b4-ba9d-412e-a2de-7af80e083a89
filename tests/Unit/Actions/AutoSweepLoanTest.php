<?php

namespace Tests\Unit\Actions;

use App\Actions\Loans\AutoSweepLoan;
use App\Enums\LoanAccountType;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\LoanProduct;
use App\Models\LoanProductPenalties;
use App\Models\LoanSweep;
use App\Models\Partner;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class AutoSweepLoanTest extends TestCase
{
    use RefreshDatabase;

    private AutoSweepLoan $action;
    private Partner $partner;
    private Customer $customer;
    private LoanProduct $loanProduct;
    private Loan $loan;

    protected function setUp(): void
    {
        parent::setUp();

        $this->action = new AutoSweepLoan();

        // Create test data
        $this->partner = Partner::factory()->create();
        $this->customer = Customer::factory()->create();

        $this->loanProduct = LoanProduct::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Auto_Sweep_Provider' => 'MTN'
        ]);

        $this->loan = Loan::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Customer_ID' => $this->customer->id,
            'Loan_Product_ID' => $this->loanProduct->id,
            'Maturity_Date' => Carbon::now()->subDays(5), // Past maturity
            'Auto_Sweep_Started' => null,
            'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
        ]);
    }

    public function test_returns_false_when_provider_is_not_mtn()
    {
        // Update loan product to use different provider
        $this->loanProduct->update(['Auto_Sweep_Provider' => 'AIRTEL']);

        Log::shouldReceive('info')
            ->once()
            ->with('Auto sweep not supported for AIRTEL');

        $result = $this->action->execute($this->loan);

        $this->assertFalse($result);
    }

    public function test_supports_mtn_provider_case_insensitive()
    {
        // Test with different cases
        $providers = ['mtn', 'MTN', 'Mtn', 'mTn'];

        foreach ($providers as $provider) {
            $this->loanProduct->update(['Auto_Sweep_Provider' => $provider]);

            // Mock the payment service to avoid actual API calls
            $this->mockPaymentService();

            // Create a fresh loan for each test to avoid state issues
            $loan = Loan::factory()->create([
                'Partner_ID' => $this->partner->id,
                'Customer_ID' => $this->customer->id,
                'Loan_Product_ID' => $this->loanProduct->id,
                'Maturity_Date' => Carbon::now()->subDays(5),
                'Auto_Sweep_Started' => null,
                'Credit_Account_Status' => LoanAccountType::BeyondTerms->value
            ]);

            $result = $this->action->execute($loan);

            $this->assertTrue($result, "Failed for provider: {$provider}");
        }
    }

    public function test_returns_false_when_loan_not_past_maturity_date()
    {
        // Set maturity date to future
        $this->loan->update(['Maturity_Date' => Carbon::now()->addDays(5)]);

        Log::shouldReceive('info')
            ->once()
            ->with(Mockery::pattern('/Loan #\d+ is not past maturity date/'));

        $result = $this->action->execute($this->loan);

        $this->assertFalse($result);
    }

    public function test_returns_false_when_loan_within_rollover_period()
    {
        // Create loan product penalty with daily rollover period
        LoanProductPenalties::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $this->loanProduct->id,
            'Recurring_Penalty_Interest_Period_Type' => 'daily',
            'Recurring_Penalty_Interest_Period_Value' => 7, // 7 days rollover
        ]);

        // Set maturity date to 3 days ago (within 7-day rollover period)
        $this->loan->update(['Maturity_Date' => Carbon::now()->subDays(3)]);

        Log::shouldReceive('info')
            ->once()
            ->with(Mockery::pattern('/Loan #\d+ is not past maturity date or still within rollover period/'));

        $result = $this->action->execute($this->loan);

        $this->assertFalse($result);
    }

    public function test_processes_loan_past_rollover_period()
    {
        // Create loan product penalty with daily rollover period
        LoanProductPenalties::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $this->loanProduct->id,
            'Recurring_Penalty_Interest_Period_Type' => 'daily',
            'Recurring_Penalty_Interest_Period_Value' => 5, // 5 days rollover
        ]);

        // Set maturity date to 10 days ago (past 5-day rollover period)
        $this->loan->update(['Maturity_Date' => Carbon::now()->subDays(10)]);

        $this->mockPaymentService();

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);
    }

    public function test_successfully_executes_auto_sweep()
    {
        $this->mockPaymentService();

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);

        // Verify transaction was created
        $this->assertDatabaseHas('transactions', [
            'Partner_ID' => $this->partner->id,
            'Type' => Transaction::REPAYMENT,
            'Status' => 'Pending',
            'Loan_ID' => $this->loan->id,
        ]);

        // Verify LoanSweep record was created
        $this->assertDatabaseHas('loan_sweeps', [
            'partner_id' => $this->partner->id,
            'loan_id' => $this->loan->id,
            'customer_id' => $this->customer->id,
        ]);
    }

    public function test_sets_auto_sweep_started_when_not_already_set()
    {
        $this->mockPaymentService();

        // Ensure Auto_Sweep_Started is null
        $this->loan->update(['Auto_Sweep_Started' => null]);

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);

        // Verify Auto_Sweep_Started was set
        $this->loan->refresh();
        $this->assertNotNull($this->loan->Auto_Sweep_Started);
    }

    public function test_sets_auto_sweep_ended_when_loan_paid_off()
    {
        $this->mockPaymentService();

        // Set loan as paid off to test Auto_Sweep_Ended logic
        $this->loan->update(['Credit_Account_Status' => LoanAccountType::PaidOff->value]);

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);

        // Verify Auto_Sweep_Ended was set
        $this->loan->refresh();
        $this->assertNotNull($this->loan->Auto_Sweep_Ended);
    }

    public function test_handles_payment_service_exception()
    {
        // Mock PaymentServiceManager to throw exception
        $mockPaymentService = Mockery::mock();
        $mockPaymentService->shouldReceive('autoCollect')
            ->andThrow(new Exception('Payment service error'));

        $mockManager = Mockery::mock(PaymentServiceManager::class);
        $mockManager->paymentService = $mockPaymentService;

        $this->app->bind(PaymentServiceManager::class, function () use ($mockManager) {
            return $mockManager;
        });

        Log::shouldReceive('error')
            ->once()
            ->with(Mockery::pattern('/Auto sweep failed for loan #\d+: Payment service error/'));

        $result = $this->action->execute($this->loan);

        $this->assertFalse($result);
    }

    public function test_wraps_execution_in_database_transaction()
    {
        DB::shouldReceive('transaction')
            ->once()
            ->andReturnUsing(function ($callback) {
                return $callback();
            });

        $this->mockPaymentService();

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);
    }

    public function test_does_not_set_auto_sweep_started_when_already_set()
    {
        $this->mockPaymentService();

        // Set Auto_Sweep_Started to a specific date
        $existingDate = Carbon::now()->subDays(2);
        $this->loan->update(['Auto_Sweep_Started' => $existingDate]);

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);

        // Verify Auto_Sweep_Started was not changed
        $this->loan->refresh();
        $this->assertEquals($existingDate->toDateTimeString(), $this->loan->Auto_Sweep_Started->toDateTimeString());
    }

    public function test_handles_penalty_with_non_daily_period_type()
    {
        // Create loan product penalty with non-daily period type
        LoanProductPenalties::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $this->loanProduct->id,
            'Recurring_Penalty_Interest_Period_Type' => 'weekly',
            'Recurring_Penalty_Interest_Period_Value' => 2,
        ]);

        $this->mockPaymentService();

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);
    }

    public function test_handles_penalty_with_zero_period_value()
    {
        // Create loan product penalty with zero period value
        LoanProductPenalties::factory()->create([
            'Partner_ID' => $this->partner->id,
            'Loan_Product_ID' => $this->loanProduct->id,
            'Recurring_Penalty_Interest_Period_Type' => 'daily',
            'Recurring_Penalty_Interest_Period_Value' => 0,
        ]);

        $this->mockPaymentService();

        $result = $this->action->execute($this->loan);

        $this->assertTrue($result);
    }

    private function mockPaymentService()
    {
        $mockPaymentService = Mockery::mock();
        $mockPaymentService->shouldReceive('autoCollect')
            ->andReturn(['status' => 'success']);

        $mockManager = Mockery::mock(PaymentServiceManager::class);
        $mockManager->paymentService = $mockPaymentService;

        $this->app->bind(PaymentServiceManager::class, function () use ($mockManager) {
            return $mockManager;
        });
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
