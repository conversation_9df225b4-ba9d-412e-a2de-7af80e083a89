<?php

namespace Tests\Unit\Rules;

use Tests\TestCase;
use App\Rules\ValidPhoneNumber;
use Illuminate\Support\Facades\Validator;

class ValidPhoneNumberTest extends TestCase
{
    protected function getValidator(array $data)
    {
        return Validator::make($data, [
            'phone' => [new ValidPhoneNumber()],
        ]);
    }

    public function test_valid_phone_numbers_pass_validation()
    {
        $validNumbers = [
            '256771234567',
            '256761234567',
            '256781234567',
            '256391234567',
            '256311234567',
            '256791234567',
            '256701234567',
            '256741234567',
            '256751234567',
        ];

        foreach ($validNumbers as $number) {
            $validator = $this->getValidator(['phone' => $number]);
            $this->assertTrue($validator->passes(), "Failed asserting that $number is valid.");
        }
    }

    public function test_invalid_length_fails_validation()
    {
        $validator = $this->getValidator(['phone' => '25677123456']); // 11 digits
        $this->assertFalse($validator->passes());

        $validator = $this->getValidator(['phone' => '2567712345678']); // 13 digits
        $this->assertFalse($validator->passes());
    }

    public function test_non_numeric_characters_fail_validation()
    {
        $validator = $this->getValidator(['phone' => '25677abcd567']);
        $this->assertFalse($validator->passes());
    }

    public function test_number_not_starting_with_256_fails_validation()
    {
        $validator = $this->getValidator(['phone' => '0771234567']);
        $this->assertFalse($validator->passes());
    }

    public function test_number_with_invalid_prefix_fails_validation()
    {
        $validator = $this->getValidator(['phone' => '256301234567']);
        $this->assertFalse($validator->passes());
    }
}
