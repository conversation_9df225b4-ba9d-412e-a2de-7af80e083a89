/.phpunit.cache
/node_modules
/.cursor
/.junie
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor

.env
.env.backup
.env.testing
.env.production
.phpunit.result.cache

docker-compose.override.yml

Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
yarn.lock
/.fleet
/.idea
/.vscode
/.yarn

!/public/assets
!/public/assets/*
/public/css
/public/js
/storage/debugbar/

# Project documentation and analysis files
pain-points.md
pain-points-v2.md
worker.log
