{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "africastalking/africastalking": "^3.0", "asantibanez/livewire-charts": "^4.1", "bacon/bacon-qr-code": "^3.0", "barryvdh/laravel-dompdf": "^3.1", "coderflex/laravel-ticket": "^2.1", "franzose/closure-table": "^6.1", "laravel/framework": "^11.0", "laravel/prompts": "^0.3.4", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "league/csv": "^9.23.0", "livewire/livewire": "^3.5", "maatwebsite/excel": "^3.1", "nevadskiy/laravel-tree": "^0.5.1", "opcodesio/log-viewer": "^3.17", "owen-it/laravel-auditing": "^13.6", "pragmarx/google2fa-laravel": "^2.2", "pragmarx/google2fa-qrcode": "^3.0", "rappasoft/laravel-livewire-tables": "^3.4", "spatie/laravel-model-status": "^1.18", "spatie/laravel-permission": "^6.9", "staudenmeir/laravel-cte": "^1.0", "twilio/sdk": "^8.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laradumps/laradumps": "^3.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"files": ["app/helper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}