<div class="card">
    <div class="card-header ">
        <div class="row">
            <div class="col-md-4">
                <h5 class="mb-0">Outstanding Loans Report</h5>
            </div>
            <div class="col-md-8 d-flex justify-content-end">
                <div class="d-flex align-items-center mx-2">
                    <input type="checkbox" id="include-written-off-loans" class="form-check-input mx-2" wire:model.live="includeWrittenOffLoans">
                    <label for="include-written-off-loans" class="me-2 form-check-label fs-6">Include Written Off Loans</label>
                </div>
                <x-end-date/>
                <x-export-buttons :with-excel="true"/>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table id="report-table" class="table table-bordered table-sm wrap">
                <thead>
                <tr>
                    <th colspan="7"></th>
                    <th colspan="4" class="text-center">Outstanding</th>
                    <th colspan="2"></th>
                </tr>
                <tr>
                    <th style="min-width: 80px">Loan #</th>
                    <th style="min-width: 200px">Customer</th>
                    <th>Phone Number</th>
                    <th class="text-end">Amount Disbursed</th>
                    <th class="text-end">Date Disbursed</th>
                    <th class="text-end" style="min-width: 100px">Expiry Date</th>
                    <th class="text-end" style="min-width: 100px">Days to Expiry</th>
                    <th class="text-end">Principal</th>
                    <th class="text-end">Interest</th>
                    <th class="text-end">Penalty</th>
                    <th class="text-end">Total Balance</th>
                    <th class="text-end">Arrears Amount</th>
                    <th class="text-end">Pending Due</th>
                </tr>
                </thead>
                <tbody>
                @forelse ($records as $record)
                    <tr>
                        <td>{{ $record->id }}</td>
                        <td>{{ $record->customer->name }}</td>
                        <td>{{ $record->customer->Telephone_Number }}</td>
                        <td class="text-end">{{ number_format($record->Facility_Amount_Granted) }}</td>
                        <td class="text-end">{{ $record->Credit_Account_Date->format('d-m-Y') }}</td>
                        <td class="text-end">{{ $record->Maturity_Date->format('d-m-Y') }}</td>
                        <td class="text-end">{{ $record->days_to_expiry }}</td>
                        <td class="text-end">{{ number_format($record->schedule_sum_principal_remaining) }}</td>
                        <td class="text-end">{{ number_format($record->schedule_sum_interest_remaining) }}</td>
                        <td class="text-end">{{ number_format($record->penalty_amount) }}</td>
                        <td class="text-end">{{ number_format($record->schedule_sum_total_outstanding + $record->penalty_amount) }}</td>
                        <td class="text-end">{{ number_format($record->total_past_due) }}</td>
                        <td class="text-end">{{ number_format($record->total_pending_due) }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="13" class="text-center">No records found</td>
                    </tr>
                @endforelse
                </tbody>
                <tfoot>
                <tr>
                    <th>Totals</th>
                    <th class="text-end">{{ count($records) }}</th>
                    <th></th>
                    <th class="text-end"><x-money :value="$records->sum('Facility_Amount_Granted')"/></th>
                    <th class="text-end"></th>
                    <th class="text-end"></th>
                    <th class="text-end"></th>
                    <th class="text-end"><x-money :value="$records->sum('schedule_sum_principal_remaining')"/></th>
                    <th class="text-end">
                        <x-money :value="$records->sum('schedule_sum_interest_remaining')" />
                    </th>
                    <th class="text-end">
                        <x-money :value="$records->sum('penalty_amount')" />
                    </th>
                    <th class="text-end">
                        <x-money :value="$records->sum('schedule_sum_total_outstanding') + $records->sum('penalty_amount')" />
                    </th>
                    <th class="text-end"><x-money :value="$records->sum('total_past_due')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('total_pending_due')" /></th>
                </tr>
                </tfoot>
            </table>

            <div class="pagination mt-5 d-flex justify-content-end">
                {{ $records->links() }}
            </div>
        </div>
    </div>
</div>
