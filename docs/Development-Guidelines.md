# Development Guidelines & Rules - LMS System

## 1. Code Architecture & Patterns

### 1.1 Laravel Best Practices
- **Models**: Use Eloquent models with proper relationships and scopes
- **Controllers**: Keep controllers thin, delegate business logic to Services/Actions
- **Services**: Create dedicated service classes for complex business logic
- **Actions**: Use single-responsibility action classes for specific operations
- **Repositories**: Implement repository pattern for complex data access
- **Events/Listeners**: Use for decoupled system notifications

### 1.2 Naming Conventions
```php
// Models: PascalCase, singular
class LoanApplication extends Model

// Controllers: PascalCase with Controller suffix
class LoanApplicationController extends Controller

// Services: PascalCase with Service suffix
class LoanService

// Actions: PascalCase with Action suffix
class ApproveLoanApplicationAction

// Database tables: snake_case, plural
loan_applications, journal_entries

// Database columns: snake_case
Customer_ID, Credit_Application_Date
```

### 1.3 File Organization
```
app/
├── Actions/           # Single-purpose action classes
├── Http/Controllers/  # HTTP controllers
├── Livewire/         # Livewire components
├── Models/           # Eloquent models
├── Services/         # Business logic services
├── Exports/          # Report export classes
├── Notifications/    # Notification classes
└── Traits/           # Reusable traits
```

## 2. Database & Data Management

### 2.1 Migration Guidelines
- Always use migrations for schema changes
- Include rollback functionality in migrations
- Use descriptive migration names with timestamps
- Add indexes for frequently queried columns
- Document complex migrations with comments

### 2.2 Model Relationships
```php
// Use proper relationship methods
public function customer()
{
    return $this->belongsTo(Customer::class, 'Customer_ID');
}

// Use eager loading to prevent N+1 queries
$loans = Loan::with(['customer', 'loan_product'])->get();

// Use scopes for reusable query logic
public function scopeActive($query)
{
    return $query->where('status', 'active');
}
```

### 2.3 Journal Entry System
- All financial transactions MUST create journal entries
- Use double-entry bookkeeping principles
- Validate debit/credit balance before saving
- Include proper transaction references and descriptions

```php
// Example journal entry creation
JournalEntry::create([
    'customer_id' => $loan->Customer_ID,
    'account_id' => $account->id,
    'amount' => $amount,
    'transactable_id' => $transaction->id,
    'transactable' => Transaction::class,
    'accounting_type' => 'Debit',
    'debit_amount' => $amount,
    'credit_amount' => 0,
]);
```

## 3. Livewire Component Guidelines

### 3.1 Component Structure
```php
class ReportComponent extends Component
{
    use ExportsData, WithPagination;
    
    public string $startDate = '';
    public string $endDate = '';
    
    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }
    
    public function render()
    {
        return view('livewire.reports.component-name', [
            'records' => $this->getReportData()
        ]);
    }
}
```

### 3.2 Report Components Pattern
- Use `ExportsData` trait for common export functionality
- Implement `printReport()` method for PDF generation
- Implement `excelExport()` method for Excel export
- Use Action classes for data retrieval
- Cache expensive queries with appropriate TTL

## 4. Reporting System

### 4.1 Report Development Pattern
1. Create Action class for data retrieval
2. Create Livewire component for UI
3. Create PDF view template
4. Create Excel export class
5. Add route and menu item

### 4.2 Report Action Structure
```php
class GetReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $perPage = 0;
    
    public function execute()
    {
        $query = Model::query()->with(['relationships']);
        
        // Apply filters
        if ($this->startDate && $this->endDate) {
            $query->whereBetween('date_column', [$this->startDate, $this->endDate]);
        }
        
        // Return paginated or full results
        return $this->perPage > 0 ? $query->paginate($this->perPage) : $query->get();
    }
    
    public function filters(array $filters): self
    {
        $this->startDate = $filters['startDate'] ?? '';
        $this->endDate = $filters['endDate'] ?? '';
        return $this;
    }
}
```

### 4.3 PDF Report Guidelines
- Use simple table structure without div tags and Bootstrap classes
- Keep PDF views minimal and focused on data presentation
- Use consistent styling across all PDF reports
- Include partner name, date filters, and generation timestamp

## 5. API Development

### 5.1 API Structure
```php
// Use API resources for consistent responses
class LoanResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'amount' => $this->Credit_Amount,
            'status' => $this->Credit_Application_Status,
        ];
    }
}

// Use form requests for validation
class StoreLoanRequest extends FormRequest
{
    public function rules()
    {
        return [
            'customer_id' => 'required|exists:customers,id',
            'amount' => 'required|numeric|min:1',
        ];
    }
}
```

### 5.2 API Authentication
- Use Sanctum for API authentication
- Validate partner codes in headers
- Implement rate limiting
- Log all API requests for audit

## 6. Security Guidelines

### 6.1 Authentication & Authorization
- Use Laravel's built-in authentication
- Implement 2FA for admin users
- Use role-based permissions with Spatie Permission package
- Validate user permissions before sensitive operations

### 6.2 Data Protection
- Encrypt sensitive data at rest
- Use HTTPS for all communications
- Implement CSRF protection
- Sanitize user inputs
- Use prepared statements for database queries

## 7. Performance Optimization

### 7.1 Database Optimization
- Use database indexes for frequently queried columns
- Implement query caching for expensive operations
- Use eager loading to prevent N+1 queries
- Consider database partitioning for large tables

### 7.2 Caching Strategy
```php
// Use cache for expensive operations
$reports = cache()->remember('report_key', 3600, function () {
    return $this->generateExpensiveReport();
});

// Use cache tags for organized invalidation
cache()->tags(['reports', 'loans'])->put('key', $data, 3600);
cache()->tags('reports')->flush(); // Invalidate all report caches
```

## 8. Testing Guidelines

### 8.1 Test Structure
- Write unit tests for business logic
- Write feature tests for user workflows
- Use factories for test data generation
- Mock external services in tests

### 8.2 Test Naming
```php
// Use descriptive test method names
public function test_loan_application_can_be_approved_by_authorized_user()
public function test_repayment_creates_proper_journal_entries()
public function test_report_filters_data_correctly()
```

## 9. Error Handling & Logging

### 9.1 Exception Handling
```php
try {
    $this->processPayment();
    DB::commit();
} catch (PaymentException $e) {
    DB::rollBack();
    Log::error('Payment processing failed', [
        'transaction_id' => $transaction->id,
        'error' => $e->getMessage(),
        'context' => $e->getContext()
    ]);
    throw $e;
}
```

### 9.2 Logging Standards
- Use appropriate log levels (info, warning, error, critical)
- Include relevant context in log messages
- Log all financial transactions
- Log API requests and responses
- Log authentication events

## 10. Code Quality Standards

### 10.1 Code Style
- Follow PSR-12 coding standards
- Use meaningful variable and method names
- Keep methods small and focused
- Add PHPDoc comments for complex methods
- Remove commented-out code before committing

### 10.2 Documentation
- Document all public methods with PHPDoc
- Include examples in complex method documentation
- Update README files when adding new features
- Document API endpoints with proper examples

## 11. Deployment & Environment

### 11.1 Environment Configuration
- Use environment variables for configuration
- Never commit sensitive data to version control
- Use different configurations for different environments
- Implement proper backup and recovery procedures

### 11.2 Queue Management
- Use queues for time-consuming operations
- Implement proper job failure handling
- Monitor queue performance and failures
- Use appropriate queue drivers for production

## 12. Specific LMS Rules

### 12.1 Loan Management
- Always validate loan eligibility before approval
- Generate loan schedules immediately after disbursement
- Create journal entries for all financial transactions
- Implement proper loan status tracking

### 12.2 Payment Processing
- Validate payment amounts and sources
- Handle partial payments correctly
- Implement proper payment allocation (principal, interest, fees, penalties)
- Send notifications for successful payments

### 12.3 Reporting
- Use journal_entries table as primary data source for financial reports
- Implement proper date filtering with complete history as default
- Follow established report component patterns
- Ensure all reports are exportable to PDF and Excel

### 12.4 SMS Notifications
- Prevent old notifications (>24 hours) from being sent
- Implement proper queue job validation
- Log all SMS sending attempts
- Handle SMS provider failures gracefully

## 13. Memory Management

### 13.1 Memory-Specific Guidelines
- For loan ledger reports: use journal_entries table as data source
- Follow pattern: web route + LoanController method + blade file + Livewire component
- Reference RepaymentReport.php as template for Livewire component structure
- Show specific loan's complete transaction history with optional date filtering
- Use 'view loan-accounts' permission and access from individual loan account page

### 13.2 Payment History Velocity Reports
- Focus on loan repayments vs loan schedule timing
- Calculate days difference between payment_due_date and actual repayment date
- Use loan schedule's updated_at column as repayment indicator
- Make exportable to PDF/Excel and list under 'Other Reports' section
- Use total_outstanding == 0 for paid installments validation

### 13.3 SMS Notification Rules
- Prevent old notifications (>24 hours in queue) from being sent when queue restarts
- Evaluate isOld method at queue job execution time, not at notification creation time
- Implement custom logic since default isOld method doesn't work properly

### 13.4 Report Display Preferences
- For PDF report views: use simple table structure without div tags and Bootstrap classes
- Display columns for loan ledger: loan id, transaction date, txn id, type, principal, interest, penalty, fees, total paid, balance due, principal balance, interest balance, fees balance, penalty balance, total balance
- Show chronological transaction history from loan application through repayments

---

**Document Version**: 1.0
**Last Updated**: 2025-06-20
**Next Review**: 2025-09-20
**Owner**: Development Team
