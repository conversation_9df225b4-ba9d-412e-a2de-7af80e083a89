<?php

namespace Database\Factories;

use App\Models\LoanProduct;
use App\Models\LoanProductPenalties;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoanProductPenalties>
 */
class LoanProductPenaltiesFactory extends Factory
{
    protected $model = LoanProductPenalties::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'Name' => fake()->randomElement(['Late Payment Penalty', 'Overdue Penalty', 'Default Penalty']),
            'Partner_ID' => Partner::factory(),
            'Calculation_Method' => fake()->randomElement(['Percentage', 'Fixed Amount']),
            'Value' => fake()->randomFloat(2, 1, 10),
            'Applicable_On' => fake()->randomElement(['Overdue Principal', 'Overdue Interest', 'Overdue Principal And Interest']),
            'Loan_Product_ID' => LoanProduct::factory(),
            'Description' => fake()->sentence(),
            'Has_Recurring_Penalty' => fake()->boolean(),
            'Recurring_Penalty_Interest_Value' => fake()->randomFloat(2, 0, 5),
            'Recurring_Penalty_Interest_Period_Type' => fake()->randomElement(['daily', 'weekly', 'monthly']),
            'Recurring_Penalty_Interest_Period_Value' => fake()->numberBetween(1, 30),
        ];
    }

    /**
     * Configure the model factory to create a daily recurring penalty.
     *
     * @return $this
     */
    public function dailyRecurring()
    {
        return $this->state(function (array $attributes) {
            return [
                'Has_Recurring_Penalty' => true,
                'Recurring_Penalty_Interest_Period_Type' => 'daily',
                'Recurring_Penalty_Interest_Period_Value' => fake()->numberBetween(1, 14),
            ];
        });
    }

    /**
     * Configure the model factory to create a non-recurring penalty.
     *
     * @return $this
     */
    public function nonRecurring()
    {
        return $this->state(function (array $attributes) {
            return [
                'Has_Recurring_Penalty' => false,
                'Recurring_Penalty_Interest_Value' => null,
                'Recurring_Penalty_Interest_Period_Type' => null,
                'Recurring_Penalty_Interest_Period_Value' => null,
            ];
        });
    }
}
