<?php

namespace Database\Factories;

use App\Models\LoanProduct;
use App\Models\LoanProductType;
use App\Models\Partner;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LoanProduct>
 */
class LoanProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LoanProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'Partner_ID' => Partner::factory(),
            'Name' => fake()->words(3, true) . ' Loan',
            'Code' => 'LP-' . strtoupper(uniqid()),
            'Loan_Product_Type_ID' => (LoanProductType::create([
                'name' => 'Mobile Loan',
                'code' => '1001',
            ]))->id,
            'Minimum_Principal_Amount' => 10000,
            'Default_Principal_Amount' => 50000,
            'Maximum_Principal_Amount' => 500000,
            'Auto_Debit' => 'No',
            'Decimal_Place' => 0,
            'Round_UP_or_Off_all_Interest' => 1,
            'Repayment_Order' => '["Penalty","Interest","Principal","Fees"]',
            'Arrears_Auto_Write_Off_Days' => 180,

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'Active',
            ];
        });
    }

    /**
     * Configure the model factory to create a personal loan product.
     *
     * @return $this
     */
    public function personal()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Personal',
                'name' => 'Personal Loan',
            ];
        });
    }

    /**
     * Configure the model factory to create a business loan product.
     *
     * @return $this
     */
    public function business()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Business',
                'name' => 'Business Loan',
                'minimum_trading_period' => 6,
            ];
        });
    }

    /**
     * Configure the model factory to create a small loan product.
     *
     * @return $this
     */
    public function smallLoan()
    {
        return $this->state(function (array $attributes) {
            return [
                'minimum_amount' => 10000,
                'maximum_amount' => 500000,
                'minimum_tenure' => 1,
                'maximum_tenure' => 3,
            ];
        });
    }

    /**
     * Configure the model factory to create a large loan product.
     *
     * @return $this
     */
    public function largeLoan()
    {
        return $this->state(function (array $attributes) {
            return [
                'minimum_amount' => 500000,
                'maximum_amount' => 10000000,
                'minimum_tenure' => 3,
                'maximum_tenure' => 24,
                'guarantor_required' => true,
                'collateral_required' => true,
            ];
        });
    }
}
