<?php

namespace Database\Factories;

use App\Models\Partner;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    protected $model = Transaction::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = fake("UG")->randomElement(['Pending', 'Completed', 'Failed']);

        return [
            'Partner_ID' => Partner::factory()->create()->id,
            'Type' => fake()->randomElement([Transaction::DEPOSIT, Transaction::WITHDRAW]),
            'Status' => $status,
            'Telephone_Number' => fake("UG")->phoneNumber(),
            'Amount' => fake("UG")->numberBetween(1000, 1000000),
            'TXN_ID' => Transaction::generateID(),
            'Provider_TXN_ID' => $status == 'Completed' ? fake("UG")->numberBetween(100000000, **********) : null
        ];
    }
}
