<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('loan_sweeps', function (Blueprint $table) {
            $table->dateTime('transaction_date')->after('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('loan_sweeps', function (Blueprint $table) {
            $table->dropColumn('transaction_date');
        });
    }
};
