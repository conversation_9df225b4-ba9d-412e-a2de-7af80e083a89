<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use App\Models\Partner;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // $supper_admin =User::create([
        //     'name' => 'Amnrose Mwaka',
        //     'email' => '<EMAIL>',
        //     'is_admin' => 1,
        //     'password' => Hash::make('password'),
        // ]);
        // $supper_admin->assignRole('Admin');

        $super_admin = User::create([
            'name' => 'LMS Admin',
            'email' => '<EMAIL>',
            'is_admin' => 1,
            'password' => Hash::make('password'),
        ]);
        // $super_admin->assignRole('Super Admin');

        // $partner_admin = User::create([
        //     'name' => 'Partner Admin',
        //     'email' => '<EMAIL>',
        //     'partner_id' => 1,
        //     'is_admin' => false,
        //     'password' => Hash::make('password'),
        // ]);
        // $partner_admin->assignRole('Partner Admin');

        // $customer_care = User::create([
        //     'name' => 'Customer Care',
        //     'email' => '<EMAIL>',
        //     'partner_id' => 1,
        //     'is_admin' => false,
        //     'password' => Hash::make('password'),
        // ]);
        // $role = Role::where('partner_id', 1)
        //     ->where('name', 'Customer Care')
        //     ->first();
        // $customer_care->assignRole($role);

        // foreach (Partner::all() as $partner) {
        //     User::factory(5)->create([
        //         'partner_id' => $partner->id,
        //     ]);
        // }
    }
}
