<?php

return [

	/*
	|--------------------------------------------------------------------------
	| Third Party Services
	|--------------------------------------------------------------------------
	|
	| This file is for storing the credentials for third party services such
	| as Mailgun, Postmark, AWS and more. This file provides the de facto
	| location for this type of information, allowing packages to have
	| a conventional file to locate the various service credentials.
	|
	*/

	'postmark' => [
		'token' => env('POSTMARK_TOKEN'),
	],

	'ses' => [
		'key' => env('AWS_ACCESS_KEY_ID'),
		'secret' => env('AWS_SECRET_ACCESS_KEY'),
		'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
	],

	'slack' => [
		'notifications' => [
			'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
			'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
		],
	],

	'gnugrid' => [
		'key' => env('GNUGRID_KEY'),
		'secret' => env('GNUGRID_SECRET'),
		'url' => env('GNUGRID_URL'),
	],

	'partners' => [
		'spiro' => [
			'username' => env('SPIRO_USERNAME'),
			'password' => env('SPIRO_PASSWORD'),
			'url' => env('SPIRO_URL'),
		],
        'spiro_atlas' => [
            'url' => env('SPIRO_ATLAS_URL'),
            'client_key' => env('SPIRO_ATLAS_CLIENT_KEY'),
            'client_secret' => env('SPIRO_ATLAS_CLIENT_SECRET'),
            'refresh_token' => env('SPIRO_ATLAS_REFRESH_TOKEN'),
            'source_id' => env('SPIRO_ATLAS_SOURCE_ID'),
        ],
        'mtn_credit_limit' => [
            'url' => env('MTN_CREDIT_LIMIT_API_URL'),
            'partner_id' => env('MTN_CREDIT_LIMIT_PARTNER_ID'),
            'api_key' => env('MTN_CREDIT_LIMIT_API_KEY'),
            'app_name' => env('MTN_CREDIT_LIMIT_APP_NAME'),
            'host_name' => env('MTN_CREDIT_LIMIT_HOST_NAME'),
        ]
	],

	'sms' => [
		'DMARK_PROD' => [
			'url' => env('DMARK_ENDPOINT'),
			'spname' => env('DMARK_USERNAME'),
			'sppass' => env('DMARK_PASSWORD', 'us-east-1'),
		],
		'DMARK_TEST' => [
			'url' => env('DMARK_TEST_ENDPOINT'),
			'spname' => env('DMARK_TEST_USERNAME'),
			'sppass' => env('DMARK_TEST_PASSWORD', 'us-east-1'),
		],
		'AFRICASTALKING_PROD' => [
			'url' => env('AFRICASTALKING_ENDPOINT'),
			'spname' => env('AFRICASTALKING_USERNAME'),
			'sppass' => env('AFRICASTALKING_PASSWORD', 'us-east-1'),
            'sender_id' => env('AFRICASTALKING_SENDER_ID'),
		],
		'AFRICASTALKING_TEST' => [
			'url' => env('AFRICASTALKING_TEST_ENDPOINT'),
			'spname' => env('AFRICASTALKING_TEST_USERNAME'),
			'sppass' => env('AFRICASTALKING_TEST_PASSWORD', 'us-east-1'),
            'sender_id' => env('AFRICASTALKING_TEST_SENDER_ID'),
		],
        'ego' => [
            'url' => env('EGOSMS_BASE_URL'),
            'username' => env('EGOSMS_USERNAME'),
            'password' => env('EGOSMS_PASSWORD'),
            'sender_id' => env('EGOSMS_SENDER_ID'),
        ],
        'africastalking' => [
            'url' => env('AFRICASTALKING_ENDPOINT'),
            'username' => env('AFRICASTALKING_USERNAME'),
            'password' => env('AFRICASTALKING_PASSWORD', 'us-east-1'),
        ]
	],

	'airtel' => [
        'production' => [
          'url' => env('AIRTEL_PRODUCTION_URL'),
          'airtel_public_key' => env('AIRTEL_PRODUCTION_PUBLIC_KEY'),
          'key' => env('AIRTEL_PRODUCTION_CLIENT_KEY'),
          'secret' => env('AIRTEL_PRODUCTION_CLIENT_SECRET'),
          'pin' => env('AIRTEL_PRODUCTION_PIN'),
        ],
        'test' => [
          'url' => env('AIRTEL_TEST_URL'),
          'airtel_public_key' => env('AIRTEL_TEST_PUBLIC_KEY'),
          'client_key' => env('AIRTEL_TEST_CLIENT_KEY'),
          'client_secret' => env('AIRTEL_TEST_CLIENT_SECRET'),
          'pin' => env('AIRTEL_TEST_PIN'),
        ],
	],
    'mtn' => [
		'production' => [
			'url' => env('MTN_API_URL'),
			'base_context' => env('MTN_API_CONTEXT', 'sp'),
			'verify_ssl' => env('MTN_API_VERIFY_SSL', true),
			'timeout' => env('MTN_API_TIMEOUT', 30),
			'debug' => env('MTN_API_DEBUG', false),
			'savings_username' => env('MTN_API_SAVINGS_USERNAME'),
			'savings_password' => env('MTN_API_SAVINGS_PASSWORD'),
			'loans_username' => env('MTN_API_LOANS_USERNAME'),
			'loans_password' => env('MTN_API_LOANS_PASSWORD'),
			'dfcu_ca_certificate' => env('MTN_API_DFCU_CA_CERTIFICATE'),
			'dfcu_certificate' => env('MTN_API_DFCU_CERTIFICATE'),
			'lms_private_key' => env('MTN_API_LMS_PRIVATE_KEY'),
			'm3_external_certificate' => env('MTN_API_M3_EXTERNAL_CERFICATE'),
		],
		'test' => [
			'url' => env('MTN_TEST_API_URL'),
			'base_context' => env('MTN_API_CONTEXT', 'sp'),
			'verify_ssl' => env('MTN_API_VERIFY_SSL', false),
			'timeout' => env('MTN_API_TIMEOUT', 30),
			'debug' => env('MTN_API_DEBUG', true),
			'savings_username' => env('MTN_API_SAVINGS_USERNAME'),
			'savings_password' => env('MTN_API_SAVINGS_PASSWORD'),
			'loans_username' => env('MTN_API_LOANS_USERNAME'),
			'loans_password' => env('MTN_API_LOANS_PASSWORD'),
			'dfcu_ca_certificate' => env('MTN_API_DFCU_CA_CERTIFICATE'),
			'dfcu_certificate' => env('MTN_API_DFCU_CERTIFICATE'),
			'lms_private_key' => env('MTN_API_LMS_PRIVATE_KEY'),
			'm3_external_certificate' => env('MTN_API_M3_EXTERNAL_CERFICATE'),
		],
	],
	'yo' => [
		'production' => [
			'url' => env('YOPAY_PRODUCTION_URL'),
			'username' => env('YOPAY_PRODUCTION_USERNAME'),
			'password' => env('YOPAY_PRODUCTION_PASSWORD'),
			'callback' => config('app.url') . '/api/yo/callback',
		],
		'test' => [
			'url' => env('YOPAY_TEST_URL'),
			'username' => env('YOPAY_TEST_USERNAME'),
			'password' => env('YOPAY_TEST_PASSWORD'),
			'callback' => config('app.url') . '/api/yo/callback',
		],
	],
    'twilio' => [
        'account' => env('TWILIO_SID'),
        'token' => env('TWILIO_AUTH_TOKEN'),
        'from' => env('TWILIO_WHATSAPP_NUMBER'),
    ],
    'nextpay' => [
        'key' => env('NEXT_PAY_KEY'),
        'secret' => env('NEXT_PAY_SECRET'),
        'url' => env('NEXT_PAY_URL'),
        'merchant_id' => env('NEXT_PAY_MERCHANT_ID'),
    ],
];
