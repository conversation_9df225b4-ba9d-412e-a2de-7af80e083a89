<?php

namespace App\Console\Commands\MTNTests;

use App\Services\CreditLimitService;
use App\Services\MockMtnApiService;
use App\Exceptions\MtnApiException;
use Illuminate\Console\Command;

class GetLoanLimit extends Command
{
    protected $signature = 'mtn:credit-limit {phone}';
    protected $description = 'Test MTN get loan limit API';

    public function handle(): int
    {
        try {
            $msisdn = $this->argument('phone');

            $this->info('Credit limit is: ' . app(CreditLimitService::class)->getCreditLimit($msisdn));

            return 0;
        } catch (MtnApiException $e) {
            $this->error('MTN API Error:');
            $this->error($e->getMessage());

            return 1;
        } catch (\Exception $e) {
            $this->error('Unexpected Error:');
            $this->error($e->getMessage());
            return 1;
        }
    }

    /**
     * Ask for input with validation
     *
     * @param string $question
     * @param string $field
     * @param array $rules
     * @param string|null $errorMessage
     * @return string
     */
    private function askValid(string $question, string $field, array $rules, ?string $errorMessage = null): string
    {
        do {
            $value = $this->ask($question);
            $validator = validator([$field => $value], [$field => $rules]);

            if ($validator->fails()) {
                $this->error($errorMessage ?? $validator->errors()->first());
                continue;
            }

            return $value;
        } while (true);
    }
}
