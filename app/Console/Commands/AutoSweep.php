<?php

namespace App\Console\Commands;

use App\Enums\LoanAccountType;
use App\Jobs\AutoSweepLoanJob;
use App\Models\Loan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AutoSweep extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:auto-sweep';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run auto sweep for loans past maturity date';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {

            $this->processAutoSweeps();

            return 0;
        } catch (\Exception $e) {
            Log::error('Error during auto sweep: ' . $e->getMessage());

            return 1;
        }
    }

    /**
     * Process auto sweeps for eligible loans
     */
    private function processAutoSweeps(): void
    {
        // Find all loans that are past maturity date and belong to loan products with auto sweep provider
        Loan::select(['id', 'loan_product_id'])
            ->whereHas('loan_product', function ($query) {
                $query->whereNotNull('Auto_Sweep_Provider')
                      ->whereIn('Auto_Sweep_Provider', ['MTN', 'NextPe']);
            })
            ->where('Maturity_Date', '<', now())
            ->whereNull('Auto_Sweep_Started') // Only process loans that haven't been auto swept yet
            ->whereNotIn('Credit_Account_Status', [
                LoanAccountType::PaidOff->value,
                LoanAccountType::WrittenOff->value,
            ])
            ->pluck('id')
            ->lazy()
            ->each(function ($loanId) {
                AutoSweepLoanJob::dispatch($loanId);
            });
    }
}
