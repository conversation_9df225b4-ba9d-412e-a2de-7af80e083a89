<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GetKyc extends Command
{
  protected $signature = 'airtel:kyc {--phone=} {--amount=} {--partner=}';
  protected $description = 'Get KYC details for a phone number. Currently only Airtel API calls.';

  public function handle(): int
  {
    $transaction = app(CreateTestTransactionAction::class)->execute(
      $this->option('amount') ?? 500,
      $this->option('phone'),
      $this->option('partner')
    );

    try {
      $api = (new PaymentServiceManager($transaction))->paymentService;
      $details = $api->kyc($this->option('phone'));

      Log::info('KYC: ' . json_encode($details, true));

      $this->info(json_encode($details, true));

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
