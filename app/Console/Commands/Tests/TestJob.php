<?php

namespace App\Console\Commands\Tests;

use App\Jobs\TransactableGetThroughPhoneJob;
use App\Models\Transaction;
use Illuminate\Console\Command;

class TestJob extends Command
{
  protected $signature = 'lms:job';
  protected $description = 'Test job, requires writing your logic';

  public function handle(): int
  {
    try {
        // Write the logic you want to test on the job.
        // Remember to comment out ShouldQueue contract otherwise job will be sent to the queue instead of running immediately here.

        return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
