<?php

namespace App\Console\Commands;

use App\Models\Loan;
use App\Models\LoanPenalty;
use App\Models\LoanProductPenalties;
use App\Models\LoanSchedule;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ApplyLoanPenalties extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:apply-loan-penalties';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $now = Carbon::now();
            // Get all loan schedules with an outstanding balance
            $loans = Loan::query()
                ->has('loan_product.penalties') // Ensures that we only get loans that have penalty to be applied.
                ->whereDate('Maturity_Date', '<', $now)
                ->whereIn('Credit_Account_Status', [Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS])
                ->get();

            foreach ($loans as $loan) {
                // todo: Implement as has one through relationship to get penalty
                // Find the loan product penalty fee
                $penalty = $loan->productPenalties()->first();

                $outstandingDays = abs($loan->getOutstandingDays());

                if ($outstandingDays >= $penalty->Recurring_Penalty_Interest_Period_Value) {
                    $outstandingDays = $penalty->Recurring_Penalty_Interest_Period_Value;
                }
                Log::info("Processing Loan ID: {$loan->id} with outstanding days: {$outstandingDays} and penalty value: {$loan->totalToBePaid()} and penalty rate: {$penalty->Value}");
                $penaltyAmount = $loan->totalToBePaid() * ($penalty->Value / 100) * $outstandingDays;
                if ($penaltyAmount <= 0) {
                    $this->info("No penalty to apply for Loan ID: {$loan->id}");
                    continue; // Skip if no penalty amount
                }
                // Check if a penalty already exists for this schedule
                $existingPenalty = LoanPenalty::where('Loan_ID', $loan->id)->first();
                if ($existingPenalty) {
                    // Update the existing penalty
                    $existingPenalty->update([
                        'Amount_To_Pay' => $penaltyAmount,
                    ]);
                    $this->info("Updated penalty of {$penaltyAmount} for Loan ID: {$loan->id}");
                } else {
                    // Create a new penalty record
                    LoanPenalty::create([
                        'Partner_ID' => $loan->Partner_ID,
                        'Loan_ID' => $loan->id,
                        'Customer_ID' => $loan->Customer_ID,
                        'Amount' => 0,
                        'Amount_To_Pay' => $penaltyAmount,
                        'Product_Penalty_ID' => $penalty->id,
                        'Loan_ID' => $loan->id,

                    ]);

                    $this->info("Applied new penalty of {$penaltyAmount} to Loan ID: {$loan->id}");
                }

                $this->info("Applied penalty of {$penaltyAmount} to Loan ID: {$loan->id}");
            }

            $this->info('Loan penalties applied successfully.');
            return 0;
        } catch (Exception $e) {
            $this->error($e->getMessage());
            Log::error($e->getMessage());
        }
    }


    protected function savePenaltyHistory(array $data)
    {
        return Storage::disk('local')->append('penalty_history.csv', $data);
    }
}
