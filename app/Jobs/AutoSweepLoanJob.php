<?php

namespace App\Jobs;

use App\Actions\Loans\AutoSweepLoan;
use App\Models\Loan;
use App\Models\LoanProductPenalties;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class AutoSweepLoanJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $loanId)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $loan = Loan::query()->findOrFail($this->loanId);

            app(AutoSweepLoan::class)->execute($loan);

        } catch (Exception $e) {
            Log::error("Auto sweep job failed for loan #{$this->loanId}: " . $e->getMessage(), [
                'loan_id' => $this->loanId,
                'error' => $e->getMessage()
            ]);

            $this->fail($e);
        }
    }
}
