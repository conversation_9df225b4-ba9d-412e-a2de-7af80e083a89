<?php

namespace App\Services;

use App\Actions\Loans\CompleteLoanApplication;
use App\Actions\Loans\CreateApprovedLoanAction;
use App\Enums\LoanAccountType;
use App\Enums\TransactionFailure;
use App\Models\Customer;
use App\Notifications\SmsNotification;
use Carbon\CarbonInterface;
use GuzzleHttp\Exception\GuzzleException;
use App\Exceptions\MtnApiException;
use App\Services\Contracts\ProvidesTransactableAPIs;
use App\Models\Transaction;
use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Actions\Loans\ProcessLoanRestructureAction;
use App\Actions\Loans\AffectAssetLoanDownPaymentAction;
use App\Actions\Savings\ProcessSavingsDepositAction;
use App\Actions\Savings\ProcessSavingsWithdrawalAction;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class MtnApiService implements ProvidesTransactableAPIs
{
    const STATUS_SUCCESS = "SUCCEEDED";
    const STATUS_PENDING = "PENDING";
    const STATUS_FAILED = "FAILED";
    const SERVICE_NOT_AVAILABLE_ON_WEEKDAYS = 'Service not available on weekdays';

    const INSUFFICIENT_BALANCE = 'Insufficient balance';

    public string $baseUrl = '';
    public string $baseContext = '';
    private bool $verifySsl = true;
    private int $timeout = 30;
    private bool $debug = false;
    private string $dfcuCaCertificate = '';
    private string $dfcuCertificate = '';
    private string $lmsPrivateKey = '';
    private string $loansUsername = '';
    private string $loansPassword = '';
    private string $savingsUsername = '';
    private string $savingsPassword = '';

    public function __construct(
        private readonly string $environment = 'test',
        private readonly array $config = []
    ) {
        $env = strtolower($this->environment);

        $this->baseUrl = Arr::get($this->config, 'mtn_url', config("services.mtn.{$env}.url")) ?? '';
        $this->baseContext = config("services.mtn.{$env}.base_context", 'sp') ?? 'sp';
        $this->verifySsl = config("services.mtn.{$env}.verify_ssl", true) ?? true;
        $this->timeout = config("services.mtn.{$env}.timeout", 30) ?? 30;
        $this->debug = config("services.mtn.{$env}.debug", false) ?? false;
        $this->dfcuCaCertificate = config("services.mtn.{$env}.dfcu_ca_certificate", '') ?? '';
        $this->dfcuCertificate = config("services.mtn.{$env}.dfcu_certificate", '') ?? '';
        $this->lmsPrivateKey = config("services.mtn.{$env}.lms_private_key", '') ?? '';
        $this->m3ExternalCertificate = config("services.mtn.{$env}.m3_external_certificate", '') ?? '';
        $this->loansUsername = config("services.mtn.{$env}.loans_username", '') ?? '';
        $this->loansPassword = config("services.mtn.{$env}.loans_password", '') ?? '';
        $this->savingsUsername = config("services.mtn.{$env}.savings_username", '') ?? '';
        $this->savingsPassword = config("services.mtn.{$env}.savings_password", '') ?? '';

//        if (! $this->isWeekend()) {
//            throw new MtnApiException('Service not available on weekdays');
//        }
    }

    public function isWeekend(): bool
    {
        if (app()->isLocal() || strtolower($this->environment) === 'test') {
            return true;
        }

        $weekend = [CarbonInterface::FRIDAY, CarbonInterface::SATURDAY, CarbonInterface::SUNDAY];

        return in_array(now()->dayOfWeek, $weekend);
    }

    public function getStatusSuccessMessage(): string
    {
        return 'SUCCEEDED';
    }

    /**
     * @throws Exception
     */
    public function disburse(
        $phone_number,
        $amount,
        $txn_reference,
        $reason = "Disbursement"
    ): array {
        try {
            $this->validatePhoneNumber($phone_number);
            $this->validateAmount($amount);
            $transaction = Transaction::query()->with('loanApplication')->firstWhere('TXN_ID', $txn_reference);

            if (! $this->isWeekend()) {
                $responseDetails = $this->getFailedResponseDetails();
                $responseDetails['payment_message'] = self::SERVICE_NOT_AVAILABLE_ON_WEEKDAYS;

                return $responseDetails;
            }

            if (app()->isLocal()) {
                $reference = mt_rand(**********, **********);
                $responseDetails = [
                    'status' => 'success',
                    'message' => self::STATUS_SUCCESS,
                    'reference' => $reference,
                    'payment_reference' => $reference,
                    'payment_message' => null,
                    'status_code' => 200,
                    'service_provider' => $this->getServiceProviderName(),
                    'data' => [],
                ];

                $partnerApplicationNumber = mt_rand(**********, **********);
                app(CompleteLoanApplication::class)->execute($transaction, $partnerApplicationNumber);

                return $responseDetails;
            }

            $transaction = Transaction::query()->with('loanApplication')->firstWhere('TXN_ID', $txn_reference);

            // First we confirm to MTN that the loan application has been completed.
            $loanApplicationCompleted = $this->loanApplicationCompleted($transaction);

            if (! $loanApplicationCompleted) {
                throw new Exception('Loan Application Error: See application logs');
            }

            $customer = Customer::query()->firstWhere('Telephone_Number', $phone_number);

            $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<p:disbursementrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend" xmlns:op="http://www.ericsson.com/em/emm/common" xmlns:java="http://www.oracle.com/XSL/Transform/java" xmlns:tns="http://www.temenos.com/T24/event/RWFInbound/CusAsyncResp">' . PHP_EOL .
                '    <sendingfri>FRI:WKD-PERSONAL/USER</sendingfri>' . PHP_EOL .
                '    <receivingfri>FRI:' . $phone_number . '/MSISDN</receivingfri>' . PHP_EOL .
                '    <amount>'. PHP_EOL .
                '        <amount>'. $amount . '</amount>'. PHP_EOL .
                '        <currency>UGX</currency>'. PHP_EOL .
                '    </amount>'. PHP_EOL .
                '    <providertransactionid>'. $txn_reference . '</providertransactionid>'. PHP_EOL .
                '    <name>'. PHP_EOL .
                '        <firstname>'. $customer->First_Name . '</firstname>'. PHP_EOL .
                '        <lastname>'. $customer->Last_Name . '</lastname>'. PHP_EOL .
                '    </name>'. PHP_EOL .
                '    <sendernote>'. $reason . '</sendernote>'. PHP_EOL .
                '    <referenceid>'. $txn_reference . '</referenceid>'. PHP_EOL .
                '</p:disbursementrequest>';

            Log::info('MTN Disbursement Payload: ' . $payload);

            $response = $this->getClient()->post('/sp/disbursement', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            if ($statusCode !== 200) {
                throw new Exception('Expected 200 response, received ' . $statusCode);
            }

            Log::info($statusCode.': Disbursement Response: ' . $body);

            return [
                'status' => $statusCode,
                'message' => self::STATUS_SUCCESS, // We must check for transaction status
                'reference' => $result['transactionid'],
                'payment_reference' => $result['transactionid'],
                'payment_message' => null,
                'status_code' => $statusCode,
                'service_provider' => $this->getServiceProviderName(),
                'data' => $result,
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                Log::error('Disbursement Error: ' . $e->getResponse()->getBody()->getContents());
            }

            return $this->getFailedResponseDetails();
        } catch (\Exception $e) {
            Log::error('Disbursement failed: ' . $e->getMessage());

            return $this->getFailedResponseDetails();
        } catch (GuzzleException $e) {
            Log::error('Disbursement failed: ' . $e->getMessage());

            return $this->getFailedResponseDetails();
        }
    }

    public function disbursementStatus($txn_id): array
    {
        return [];
    }

    /**
     * Debit money from an agent's wallet
     *
     * @param string $phone_number Agent's phone number (format: 256XXXXXXXXX)
     * @param float $amount Amount to debit
     * @param string $txn_reference External transaction reference
     * @param string $reason Reason for the debit
     * @return array Response from the API
     * @throws MtnApiException|GuzzleException
     */
    public function collect(
        string $phone_number,
        $amount,
        string $txn_reference,
        string $reason = "Repayment"
    ): array {

        $this->validatePhoneNumber($phone_number);
        $this->validateAmount($amount);

        $transaction = Transaction::query()->firstWhere('txn_id', $txn_reference);

        try {

            if (!$transaction) {
                throw new Exception("Transaction with reference $txn_reference not found");
            }

            if (app()->isLocal()) {
                $reference = mt_rand(**********, **********);

                $processed =$this->processLoanRepayment($transaction);

                // todo: Make this update in the processLoanRepayment method
                $transaction->update([
                    'Status' => $processed ? 'Completed' : 'Failed',
                    'Payment_Reference' => $reference,
                ]);

                $this->closeLoan($transaction);

                return [
                    'status' => 'success',
                    'message' => self::STATUS_SUCCESS,
                    'reference' => $reference,
                    'payment_reference' => $reference,
                    'payment_message' => null,
                    'status_code' => 200,
                    'service_provider' => $this->getServiceProviderName(),
                    'data' => [],
                ];
            }

            $details = [
                'phone_number' => $phone_number,
                'amount' => $amount,
                'reference' => $txn_reference,
                'reason' => $reason,
            ];
            $payload = $this->getBankDebitPayload($details);

            Log::info('MTN Payload - Collection: ' . $payload);

            $response = $this->getClient()->post('/sp/bankdebit', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            Log::info($statusCode.': Collect Response: ' . $body);

            $responseDetails = [
                'status' => $statusCode,
                'message' => self::STATUS_PENDING,
                'reference' => data_get($result, 'transactionid'),
                'payment_reference' => data_get($result, 'transactionid'),
                'payment_message' => null,
                'status_code' => $statusCode,
                'service_provider' => $this->getServiceProviderName(),
                'data' => $result,
            ];

            if (Arr::get($result, 'status') === 'SUCCESSFUL') {
                $responseDetails['message'] = self::STATUS_SUCCESS;
                // First process the payment
                $this->processLoanRepayment($transaction);

                // todo: Make this update in the processLoanRepayment method
                $transaction->update([
                    'Status' => 'Completed',
                    'Payment_Reference' => $responseDetails['payment_reference'],
                ]);

                // Unlink for cleared loans
                $this->closeLoan($transaction);

                return $responseDetails;
            }

            if ($result['status'] === 'FAILED') {
                $responseDetails['message'] = self::STATUS_FAILED;

                $transaction->update([
                    'Status' => 'Failed',
                ]);
                // todo: Should we send an SMS to the customer?
                //$transaction->customer->notify(new SmsNotification('Unfortunately, your loan application of UGX ' . number_format($transaction->Amount)  . ' from ' . $transaction->partner->Institution_Name . ' was unsuccessful. Please try again.', $transaction->Telephone_Number, $transaction->customer->id, $transaction->partner->id));
            }

            Log::info('Collection Response Details: ' . json_encode($responseDetails));

            return $responseDetails;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $responseDetails = [
                'status' => 500,
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => TransactionFailure::RequestError->value, // General error message
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];

            if ($e->hasResponse()) {
                $responseContent = $e->getResponse()->getBody()->getContents();

                if (str($responseContent)->contains('INSUFFICIENT_BALANCE')) {
                    $responseDetails['payment_message'] = TransactionFailure::InsufficientBalance->value;
                }

                Log::error('MTN Collect Error: ' . $responseContent);
            }

            $transaction->update([
                'Status' => 'Failed',
                'Narration' => Arr::get($responseDetails, 'payment_message'),
                'Payment_Service_Provider' => $this->getServiceProviderName(),
            ]);

            return $responseDetails;
        } catch (\Exception $e) {
            Log::error('Collection failed: ' . $e->getMessage());

            return [
                'status' => 500,
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => TransactionFailure::ProcessingError->value,
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        }
    }

    public function autoCollect(Transaction $transaction): array
    {
        if (app()->isLocal()) {
            $reference = mt_rand(**********, **********);
            $responseDetails = [
                'status' => 'success',
                'message' => self::STATUS_PENDING,
                'reference' => $reference,
                'payment_reference' => $reference,
                'payment_message' => null,
                'status_code' => 200,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [
                    'status' => 'SUCCESSFUL',
                    'amount' => [
                        'amount' => $transaction->Amount,
                        'currency' => 'UGX'
                    ]
                ],
            ];

            return $this->processAutoCollection($transaction, $responseDetails);
        }

        $details = [
            'phone_number' => $transaction->Telephone_Number,
            'amount' => $transaction->Amount,
            'reference' => $transaction->TXN_ID,
            'reason' => 'Auto Sweep Repayment',
        ];
        $payload = $this->getBankDebitPayload($details, false);

        Log::info('MTN Payload - Auto Collection: ' . $payload);

        try {
            $response = $this->getClient()->post('/sp/bankdebit', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            Log::info($statusCode.': Auto Collect Response: ' . $body);

            $responseDetails = [
                'status' => $statusCode,
                'message' => self::STATUS_PENDING,
                'reference' => data_get($result, 'transactionid'),
                'payment_reference' => data_get($result, 'transactionid'),
                'payment_message' => null,
                'status_code' => $statusCode,
                'service_provider' => $this->getServiceProviderName(),
                'data' => $result,
            ];

            Log::info('Auto Collect Response Details: ' . json_encode($responseDetails));

            return $this->processAutoCollection($transaction, $responseDetails);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                Log::error('MTN Auto Collect Error: ' . $e->getResponse()->getBody()->getContents());
            }

            return [
                'status' => 500,
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => 'Auto Collect failed: ' . str($e->getMessage())->limit(50),
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            Log::error('Auto Collect failed: ' . $e->getMessage());

            return [
                'status' => 500,
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => 'Auto Collect failed: ' . str($e->getMessage())->limit(50),
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        }
    }

    protected function getBankDebitPayload(array $details, bool|null $simpleSweep = null): string
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns2:bankdebitrequest xmlns:ns2="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
            '    <fromfri>FRI:' . $details['phone_number'] . '/MSISDN</fromfri>' . PHP_EOL .
            '    <tofri>FRI:WKD-PERSONAL/USER</tofri>'. PHP_EOL .
            '    <amount>'. PHP_EOL .
            '        <amount>'. $details['amount'] . '</amount>'. PHP_EOL .
            '        <currency>UGX</currency>'. PHP_EOL .
            '    </amount>'. PHP_EOL .
            '    <externaltransactionid>' . $details['reference'] . '</externaltransactionid>'. PHP_EOL .
            '    <frommessage>Loan Repayment.</frommessage>'. PHP_EOL .
            '    <referenceid>' . $details['reference'] . '</referenceid>'. PHP_EOL;

        if (! is_null($simpleSweep)) {
            $payload .= '    <simplesweep>' . ($simpleSweep ? 'true' : 'false') . '</simplesweep>'. PHP_EOL;
        }

        return $payload . '</ns2:bankdebitrequest>';
    }

    public function collectionStatus($collection_txn_id): array
    {
        return [];
    }

    public function unlinkLoan(Transaction $transaction): bool
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:'. $transaction->customer->loan_application_account_number . '@WKD-PERSONAL/SP</fri>' . PHP_EOL .
            '    <msisdn>' . $transaction->Telephone_Number . '</msisdn>'. PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        $result = $this->unlink($payload);

        if (! $result) {
            return false;
        }

        $this->removeLoanApplicationNumberFromCustomer($transaction->customer);

        return true;
    }

    /**
     */
    public function unlinkCustomer(Customer $customer): bool
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:'. $customer->optional_savings_account_number . '@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <msisdn>' . $customer->Telephone_Number . '</msisdn>'. PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        return $this->unlink($payload);
    }

    /**
     * Complete customer registration process
     *
     * @param Customer $customer
     * @return bool Response from the API
     * @throws GuzzleException
     */
    public function customerRegistrationCompleted(Customer $customer): bool
    {
        if (app()->isLocal()) {
            return true;
        }

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:customerregistrationcompletedrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
            '    <resource>FRI:' . $customer->Telephone_Number . '@WKD-SAVINGS/SP</resource>' . PHP_EOL .
            '    <customerid>' . $customer->Telephone_Number . '</customerid>' . PHP_EOL .
            '    <status>REGISTERED</status>' . PHP_EOL .
            '    <savingsaccount>' . PHP_EOL .
            '        <accountnumber>' . $customer->optional_savings_account_number . '</accountnumber>' . PHP_EOL .
            '        <status>ACTIVE</status>' . PHP_EOL .
            '        <balance>' . PHP_EOL .
            '            <amount>0</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '        </balance>' . PHP_EOL .
            '        <savingsaccounttype>SAVINGS</savingsaccounttype>' . PHP_EOL .
            '    </savingsaccount>' . PHP_EOL .
            '    <message>Successfully registered with MoMoney.</message>' . PHP_EOL .
            '</p:customerregistrationcompletedrequest>';

        Log::info('MTN CR Completed Payload: ' . $payload);

        try {
            $response = $this->getClient('savings')->post('/sp/customerregistrationcompleted', [
                'body' => $payload
            ]);

            $responseDetails = $this->xmlToArray($response->getBody()->getContents());
            Log::info('MTN CR Completed Response: ' . json_encode($responseDetails));

            return true;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                Log::error('Customer Registration Error: ' . $e->getResponse()->getBody()->getContents());
            }

            return false;
        }
    }

    /**
     * Complete loan application process
     *
     * @param Transaction $transaction
     * @return bool
     * @throws \Exception|GuzzleException
     */
    public function loanApplicationCompleted(Transaction $transaction): bool
    {
        $loanApplicationSession = $transaction->loanApplication->loan_session;
        $loanApplicationSession->loadMissing('loanProductTerm');
        $loanProductTerm = $loanApplicationSession->loanProductTerm;
        $partnerApplicationNumber = $this->generateNumber();
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:loanapplicationcompletedrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend" xmlns:op="http://www.ericsson.com/em/emm/common" xmlns:java="http://www.oracle.com/XSL/Transform/java" xmlns:tns="http://www.temenos.com/T24/event/RWFInbound/CusAsyncResp">' . PHP_EOL .
            '    <resource>FRI:' . $transaction->Telephone_Number . '@WKD-PERSONAL/SP</resource>' . PHP_EOL .
            '    <customerid>' . $transaction->Telephone_Number . '</customerid>' . PHP_EOL .
            '    <loanaccount>' . PHP_EOL .
            '        <accountnumber>' . $partnerApplicationNumber . '</accountnumber>' . PHP_EOL .
            '        <status>APPROVED</status>' . PHP_EOL .
            '        <due>' . PHP_EOL .
            '            <amount>' . (int) $transaction->Amount . '</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '        </due>' . PHP_EOL .
            '        <duedate>' . $loanApplicationSession->Maturity_Date->toDateString() . '</duedate>' . PHP_EOL .
            '        <tenor>' . $loanProductTerm->Value . '</tenor>' . PHP_EOL .
            '        <loantype>PERSONAL</loantype>' . PHP_EOL .
            '        <interest>0</interest>' . PHP_EOL .
            '    </loanaccount>' . PHP_EOL .
            '    <message>Hello, your loan request with ' . $transaction->loanApplication->loan_product->Name . ' has been accepted. Please wait for the funds to be deposited to your MM account.</message>' . PHP_EOL .
            '</p:loanapplicationcompletedrequest>';

        Log::info('MTN LA Payload: ' . $payload);

        try {
            $response = $this->getClient()
                ->request('POST', '/sp/loanapplicationcompleted', [
                    'body' => $payload
                ]);

            $body = $response->getBody()->getContents();
            Log::info('MTN LA Response: ' . $body);
            // Process successful response
            $details = $this->xmlToArray($body);
            Log::info(json_encode($details));

            if (str($body)->contains('errorcode')) {
                throw new \Exception('MTN LA Error: ' . json_encode($details));
            }

            app(CompleteLoanApplication::class)->execute($transaction, $partnerApplicationNumber);

            Log::info('MTN LA Completed: ' . json_encode($details));

            return true;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                $body = $e->getResponse()->getBody()->getContents();
                Log::error('MTN LA Error: ' . $body);

                if (str($body)->contains('ERROR_MAX_ACTIVE_CASH_LOAN_LIMIT_EXCEEDED')) {
                    /**
                     * MTN rejected the application because the customer has an active loan within their systems.
                     * We need to unlink the loan application from MTN.
                     */
                    $transaction->refresh();
                    $this->unlinkLoan($transaction);

                    $transaction->customer->notify(new SmsNotification('Sorry, you have an active MoMo loan. Kindly repay it to unlock access to ' . $transaction->loanApplication->loan_product->Name, $transaction->Telephone_Number, $transaction->customer->id, $transaction->partner->id));
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('MTN LA Error: ' . $e->getMessage(), $e->getTrace());

            return false;
        }
    }

    private function xmlToArray(string $xml): array
    {
        if (empty($xml)) {
            return [];
        }

        // Implement XML to array conversion
        // This is a simplified example
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }

    /**
     * Validate a phone number format
     *
     * @param string $msisdn Phone number to validate
     * @throws MtnApiException
     */
    private function validatePhoneNumber(string $msisdn): void
    {
        if (!preg_match('/^256[0-9]{9}$/', $msisdn)) {
            throw new MtnApiException('Invalid phone number format. Must be in format: 256XXXXXXXXX');
        }
    }

    /**
     * Validate amount is positive
     *
     * @param float $amount Amount to validate
     * @throws MtnApiException
     */
    private function validateAmount(float $amount): void
    {
        if ($amount <= 0) {
            throw new MtnApiException('Amount must be greater than 0');
        }
    }

    public function processCallback(Transaction $transaction): bool
    {
        try {
            $txn_reference = $transaction->TXN_ID;

            if (strtoupper($transaction->Status) == self::STATUS_SUCCESS) {
                throw new Exception("Transaction with reference $txn_reference already processed");
            }

            if ($transaction->Type == Transaction::DISBURSEMENT) {
                return $this->processLoanDisbursement($transaction);
            } else if ($transaction->Type == Transaction::DEPOSIT) {
                return $this->processSavingsDeposit($transaction);
            } else if ($transaction->Type == Transaction::WITHDRAW) {
                return $this->processSavingsWithdraw($transaction);
            } else if ($transaction->Type == Transaction::REPAYMENT) {
                return $this->processLoanRepayment($transaction);
            } else if ($transaction->Type == Transaction::RESTRUCTURE) {
                return $this->processLoanRestructure($transaction);
            } else if ($transaction->Type == Transaction::DOWNPAYMENT) {
                return $this->processAssetLoanDownpayment($transaction);
            } else {
                throw new Exception("Invalid transaction type");
            }
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
    }

    private function processLoanRepayment($transaction): bool
    {
        return app(ProcessLoanRepaymentAction::class)->execute($transaction);
    }

    private function processLoanDisbursement($transaction): bool
    {
        return app(CreateApprovedLoanAction::class)->execute($transaction);
    }

    private function processSavingsDeposit($transaction): bool
    {
        return app(ProcessSavingsDepositAction::class)->execute($transaction);
    }

    private function processSavingsWithdraw($transaction): bool
    {
        return app(ProcessSavingsWithdrawalAction::class)->execute($transaction);
    }

    private function processLoanRestructure($transaction): bool
    {
        return app(ProcessLoanRestructureAction::class)->execute($transaction);
    }

    private function processAssetLoanDownpayment($transaction): bool
    {
        return app(AffectAssetLoanDownPaymentAction::class)->execute($transaction);
    }

    /**
     * @param string $type
     * @return Client
     */
    public function getClient(string $type = 'loans'): Client
    {
        $auth = [$this->loansUsername, $this->loansPassword];

        if ($type === 'savings') {
            $auth = [$this->savingsUsername, $this->savingsPassword];
        }

        return new Client([
            'base_uri' => $this->baseUrl,
            'auth' => $auth,
            'headers' => [
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml',
            ],
            'verify' => false,
            'cert' => storage_path($this->dfcuCertificate),
            'ssl_key' => storage_path($this->lmsPrivateKey),
            'curl' => [
                CURLOPT_CAINFO => storage_path($this->dfcuCaCertificate),
            ],
        ]);
    }

    public function getBaseUrl()
    {
        return $this->baseUrl;
    }

    public function getLoansUsername()
    {
        return $this->loansUsername;
    }

    public function getLoansPassword()
    {
        return $this->loansPassword;
    }

    public function getDfcuCertificate()
    {
        return $this->dfcuCertificate;
    }

    public function getLmsPrivateKey()
    {
        return $this->lmsPrivateKey;
    }

    public function getDfcuCaCertificate()
    {
        return $this->dfcuCaCertificate;
    }

    public function getServiceProviderName(): string
    {
        return 'MTN';
    }

    public function getBaseContext(): string
    {
        return $this->baseContext;
    }

    public function getTimeout(): int
    {
        return $this->timeout;
    }

    public function getVerifySsl(): bool
    {
        return $this->verifySsl;
    }

    public function getDebug(): bool
    {
        return $this->debug;
    }

    public function getSavingsUsername(): string
    {
        return $this->savingsUsername;
    }

    public function getSavingsPassword(): string
    {
        return $this->savingsPassword;
    }

    public function getM3ExternalCertificate(): string
    {
        return $this->m3ExternalCertificate;
    }

    /**
     * @param string $payload
     * @return bool
     */
    public function unlink(string $payload): bool
    {
        try {
            Log::info('MTN Unlink Payload: ' . $payload);

            $response = $this->getClient()
                ->request('POST', '/sp/unlinkfinancialresourceinformation', [
                    'body' => $payload
                ]);

            $details = $this->xmlToArray($response->getBody()->getContents());

            Log::info(json_encode($details));
            if (!empty($details)) {
                throw new \Exception(json_encode($details));
            }

            Log::info('MTN Unlink Completed: ' . json_encode($details));

            return true;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error('MTN Unlink Request Exception: ' . $e->getMessage());
            if ($e->hasResponse()) {
                Log::error('MTN Unlink Request Exception: ' . $e->getResponse()->getBody()->getContents());
            }

            return false;
        } catch (\Exception|GuzzleException $e) {
            Log::error('MTN Unlink General Error: ' . $e->getMessage());

            return false;
        }
    }

    private function generateNumber(): int
    {
        return mt_rand(**********, **********);
    }

    /**
     * @param Transaction $transaction
     * @return bool
     */
    public function closeLoan(Transaction $transaction): bool
    {
        $transaction->refresh();

        $loan = $transaction->loan->fresh();

        /**
         * If the closing of the loan we only log but not throw an error.
         * This way the logic does not have to break. The customer can close the loan using the menu.
         */
        if ($loan->Credit_Account_Status !== LoanAccountType::PaidOff->value) {
            return false;
        }

        return $this->unlinkLoan($transaction);
    }

    private function processAutoCollection(Transaction $transaction, array $responseDetails): array
    {
        $status = data_get($responseDetails, 'data.status');

        if (strtoupper($status) !== 'SUCCESSFUL') {
            $responseDetails['message'] = self::STATUS_FAILED;

            $transaction->update([
                'Status' => 'Failed',
            ]);

            return $responseDetails;
        }

        $amount = data_get($responseDetails, 'data.amount.amount');
        $responseDetails['message'] = self::STATUS_SUCCESS;

        /**
         * When the debit request is sent, MTN will sweep what it finds on the agent's wallet
         * So we have to update our transaction record with that amount.
         */
        if ($amount < $transaction->Amount) {
            $transaction->Amount = $amount;
        }

        // First process the payment
        $this->processLoanRepayment($transaction);

        $transaction->Status = 'Completed';
        $transaction->Payment_Service_Provider = $this->getServiceProviderName();
        $transaction->Provider_TXN_ID = data_get($responseDetails, 'data.transactionid');
        $transaction->Payment_Reference = data_get($responseDetails, 'data.transactionid');
        $transaction->save();

        $this->closeLoan($transaction);

        return $responseDetails;
    }

    /**
     * @return array
     */
    public function getFailedResponseDetails(): array
    {
        return [
            'status' => 'TF',
            'message' => self::STATUS_FAILED,
            'reference' => null,
            'payment_reference' => null,
            'payment_message' => null,
            'status_code' => 500,
            'service_provider' => $this->getServiceProviderName(),
            'data' => [],
        ];
    }

    /**
     * @param Customer $customer
     * @return bool
     */
    public function removeLoanApplicationNumberFromCustomer(Customer $customer): bool
    {
        $options = $customer->options;
        data_set($options, 'loanaccounts.loanaccount.accountnumber', null);
        $customer->options = $options;

        return $customer->save();
    }
}
