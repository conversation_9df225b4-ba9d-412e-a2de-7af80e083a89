<?php

namespace App\Http\Controllers;

use App\Models\LoanProduct;
use Illuminate\Http\Request;
use App\Models\LoanProductAddon;

class LoanProductAddonController extends Controller
{

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $loanProduct = LoanProduct::findOrFail($request->Loan_Product_ID);
            LoanProductAddon::create($request->all() + ['Partner_ID' => $loanProduct->Partner_ID]);
            return back()->with('success', 'Loan Product Addon created successfully');
        } catch (\Throwable $th) {
            return back()->with('error', 'Failed to create Loan Product Addon');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LoanProductAddon $loanProductAddon)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'Name' => 'required|string|max:255',
            'Repayment_Cycle' => 'required|string|in:' . implode(',', \App\Models\LoanSchedule::REPAYMENT_FREQUENCIES),
            'Interest_Rate' => 'required|numeric|min:0',
            'Interest_Calculation_Method' => 'required|string|in:' . implode(',', \App\Models\LoanSchedule::SUPPORT_INTEREST_METHODS),
            'Interest_Cycle' => 'required|string|in:' . implode(',', \App\Models\LoanSchedule::INTEREST_CYCLES),
            'Term' => 'required|integer|min:1',
            'Charge_Interest_On' => 'required|string|in:On Loan Principal,On Amount Given,None',
            'Amount' => 'nullable|numeric|min:0',
            'Downpayment_Percentage' => 'required|numeric|min:0|max:100',
        ]);

        try {
            // Update the LoanProductAddon with validated data
            $loanProductAddon->update($validated);

            // Redirect back with success message
            return redirect()->back()->with('success', 'Loan Product Add-on updated successfully.');
        } catch (\Exception $e) {
            // Handle exception and return an error message
            return redirect()->back()->withErrors(['error' => 'Failed to update Loan Product Add-on: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LoanProductAddon $loanProductAddon)
    {
        try {
            // Delete the LoanProductAddon
            $loanProductAddon->delete();

            // Redirect back with success message
            return redirect()->back()->with('success', 'Loan Product Add-on deleted successfully.');
        } catch (\Exception $e) {
            // Handle exception and return an error message
            return redirect()->back()->withErrors(['error' => 'Failed to delete Loan Product Add-on: ' . $e->getMessage()]);
        }
    }
}
