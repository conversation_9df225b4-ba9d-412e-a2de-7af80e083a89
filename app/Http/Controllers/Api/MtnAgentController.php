<?php

namespace App\Http\Controllers\Api;

use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Actions\RegisterCustomerAction;
use App\Enums\LoanAccountType;
use App\Http\Controllers\Controller;
use App\Jobs\CompleteCustomerRegistration;
use App\Models\Customer;
use App\Models\Loan;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MtnAgentController extends Controller
{
    public function customerDetails(Request $request): string
    {
        $details = $this->xmlToArray($request->getContent());

        $stringWithPhone = Arr::get($details, 'resource');
        $phoneNumber = str($stringWithPhone)->after('FRI:')->before('/')->toString();
        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (empty($customer) || $customer->doesNotHaveOptionalSavingsAccount()) {
            // Create the XML structure as a string
            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:getcustomerdetailsresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '   <status>UNREGISTERED</status>' . PHP_EOL .
                '   <savingsaccounts/>' . PHP_EOL .
                '   <loanaccounts/>' . PHP_EOL .
                '</ns0:getcustomerdetailsresponse>';
        }

        $response = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:getcustomerdetailsresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
            . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
            . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
            . 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" '
            . 'xsi:schemaLocation="http://www.ericsson.com/em/emm/sp/backend savingandlendingbackend.xsd'
            . '">' . PHP_EOL .
            '   <status>REGISTERED</status>' . PHP_EOL .
            '   <savingsaccounts>' . PHP_EOL .
            '      <savingsaccount>' . PHP_EOL .
            '         <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
            '         <status>' . data_get($customer->options, 'savingsaccount.status') . '</status>' . PHP_EOL .
            '         <balance>' . PHP_EOL .
            '            <amount>' . data_get($customer->options, 'savingsaccount.balance.amount') . '</amount>' . PHP_EOL .
            '            <currency>' . data_get($customer->options, 'savingsaccount.balance.currency') . '</currency>' . PHP_EOL .
            '         </balance>' . PHP_EOL .
            '         <savingsaccounttype>' . data_get($customer->options, 'savingsaccount.savingsaccounttype') . '</savingsaccounttype>' . PHP_EOL .
            '      </savingsaccount>' . PHP_EOL .
            '   </savingsaccounts>' . PHP_EOL;

        if ($customer->activeLoans->isEmpty()) {
            return $response . '   <loanaccounts/>' . PHP_EOL .
                '</p:getcustomerdetailsresponse>';
        }

        // todo: Add loan account to the details
        $response = $response . '   <loanaccounts>' . PHP_EOL;

        /**
         * todo: Customer should have only one active loan for MTN
         * So we should only return the latest active loan and update the loan account template
         */
        $loan = $customer->activeLoans->first();
        $loanAccount = str($this->getLoanAccountTemplate())
            ->replace('_accountNumber', $loan->customer->loan_application_account_number)
            ->replace('_amount', $loan->totalOutstandingBalance())
            ->replace('_dueDate', $loan->Maturity_Date->toDateString())
            ->toString();
        $response .= $loanAccount . PHP_EOL;

        return $response . '   </loanaccounts>' . PHP_EOL .
            '</p:getcustomerdetailsresponse>';
    }

    public function registerCustomer(Request $request, RegisterCustomerAction $registerAction): string
    {
        $details = $this->xmlToArray($request->getContent());

        try {
            Validator::validate(
                $details,
                [
                    'name.firstname' => 'required|string',
                    'name.lastname' => 'required|string',
                    'dob' => 'required|date',
                    'idtype' => 'required|string',
                    'idnumber' => 'required|string',
                    //'idexpiry' => 'required|nullable',
                    'gender' => 'required|string'
                ]
            );

            // todo: Do this in the dispatched job
            $customer = $registerAction->useMtnKyc()->execute($details);
            $options = $customer->options;
            $options['savingsaccount'] = [
                'accountnumber' => $customer->account_number,
                'status' => 'ACTIVE',
                'balance' => [
                    'amount' => 0,
                    'currency' => 'UGX'
                ],
                'savingsaccounttype' => 'SAVINGS'
            ];
            $options['opt_in_at'] = now()->toDateTimeString();
            $options['opt_out_at'] = null; // Even if this customer had earlier opted out, we are resetting.

            $customer->options = $options;
            $customer->save();

            dispatch(new CompleteCustomerRegistration($customer));

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL
                . '<p:customerregistrationresponse xmlns:p="http://www.ericsson.com/em/emm/sp/backend" '
                . 'xmlns:iso="urn:iso:std:iso:20022:tech:xsd" '
                . 'xmlns:op="http://www.ericsson.com/em/emm/common" '
                . 'xmlns:soap-env="http://schemas.xmlsoap.org/soap/envelope/">' . PHP_EOL
                . '    <status>PENDING</status>' . PHP_EOL
                . '    <message>Your request to register for Weekend Agent Loan service has been received, please wait for a confirmation shortly</message>' . PHP_EOL
                . '</p:customerregistrationresponse>';
        } catch (\Exception $e) {
            Log::error('MTN Register Customer Error: ' . $e->getMessage());

            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_CREATED</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationresponse>';

            return response($xml, 500)
                ->header('Content-Type', 'application/xml');
        }
    }

    public function completeCustomerRegistration(Request $request): string
    {
        $details = $this->xmlToArray($request->getContent());

        $resource = Arr::get($details, 'resource');
        $phoneNumber = str($resource)->after('FRI:')->before('@')->toString();
        $customer = Customer::query()->firstWhere('Telephone_Number', $phoneNumber);

        if (! $customer) {
            Log::debug('MTN Completing Customer Registration Error:' . $phoneNumber . ' from ' . $resource);

            return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
                '   <errorcode>ACCOUNT_NOT_FOUND</errorcode>' . PHP_EOL .
                '</ns0:customerregistrationcompletedresponse>';
        }

        $options = $customer->options;
        $options['savingsaccount'] = Arr::get($details, 'savingsaccount');
        $customer->options = $options;
        $customer->save();

        // Customer has successfully registered with us.
        return '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:customerregistrationcompletedresponse xmlns:ns0="http://www.ericsson.com/em/emm/sp/frontend"/>';
    }

    /**
     * @throws BindingResolutionException
     */
    public function bankDebitCompleted(Request $request): string
    {
        $details = $this->xmlToArray($request->getContent());
        Log::info('BD Request: ' . json_encode($details));
        $phoneNumber = str(Arr::get($details, 'fromfri'))->after('FRI:')->before('/')->toString();
        $customer = Customer::query()->with('activeLoans')->firstWhere('Telephone_Number', $phoneNumber);
        $loan = $customer->activeLoans->first();

        if (empty($loan)) {
            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="LOAN_APPLICATION_NOT_FOUND">' . PHP_EOL .
                '  <arguments name="reason" value="Loan account not found"/>' . PHP_EOL .
                '</ns0:errorResponse>';

            Log::error($content);

            return response()
                ->make($content)
                ->setStatusCode(500)
                ->header('Content-Type', 'application/xml');
        }

        $balance = $loan->totalOutstandingBalance();
        $sweptAmount = +data_get($details, 'amount.amount', 0);

        if ($balance < $sweptAmount) {
            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="AUTHORIZATION_MAX_TRANSFER_AMOUNT">' . PHP_EOL .
                '  <arguments name="reason" value="Amount is greater than the outstanding balance"/>' . PHP_EOL .
                '</ns0:errorResponse>';

            Log::error($content);

            return response()
                ->make($content)
                ->setStatusCode(500)
                ->header('Content-Type', 'application/xml');
        }

        DB::beginTransaction();

        try {
            $transactionRecord = [
                'Partner_ID' => $loan->Partner_ID,
                'Type' => Transaction::REPAYMENT,
                'Amount' => data_get($details, 'amount.amount', 0),
                'Status' => 'Pending',
                'Telephone_Number' => $customer->Telephone_Number,
                'TXN_ID' => Transaction::generateID()->toString(),
                'Loan_ID' => $loan->id,
                'Loan_Application_ID' => $loan->Loan_Application_ID,
                'Provider_TXN_ID' => data_get($details, 'transactionid'),
                'Payment_Reference' => data_get($details, 'transactionid'),
            ];

            $transaction = new Transaction($transactionRecord);
            $transaction->save();

            // todo: Process repayment
            app(ProcessLoanRepaymentAction::class)->execute($transaction);

            $transaction->Status = 'Completed';
            $transaction->save();

            // Unlink for cleared loans
            $loan = $transaction->loan->fresh();

            // todo: refactor as this check is already done in the closeLoan method.
            if ($loan->Credit_Account_Status === LoanAccountType::PaidOff->value) {
                $loan->update([
                    'Auto_Sweep_Ended' => now(),
                ]);

                // todo: Refactor so that extra methods not part of payment processing are called directly by the specific or other extracted service.
                $api = (new PaymentServiceManager($transaction))->paymentService;

                if (! $api->closeLoan($transaction)) {
                    Log::error('Failed closing loan: ' . $loan->id);
                }
            }

            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<bac:bankdebitcompletedresponse xmlns:bac="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
                '  <externaltransactionid>' . $transaction->TXN_ID . '</externaltransactionid>' . PHP_EOL .
                '  <status>SUCCESSFUL</status>' . PHP_EOL .
                '</bac:bankdebitcompletedresponse>';
            Log::info('BD Completed: ' . $content);

            DB::commit();

            return response()
                ->make($content)
                ->setStatusCode(200)
                ->header('Content-Type', 'application/xml');
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('BDC Error: ' . $e->getMessage());

            $content = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<ns0:<ns0:errorResponse xmlns:ns0="http://www.ericsson.com/lwac" errorcode="ERROR_RESPONSE">' . PHP_EOL .
                '  <arguments name="reason" value="Error processing transaction"/>' . PHP_EOL .
                '</bac:errorResponse>';

            return response()
                ->make($content)
                ->setStatusCode(500)
                ->header('Content-Type', 'application/xml');
        }
    }

    private function xmlToArray(string $xml): array
    {
        // Implement XML to array conversion
        // This is a simplified example
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }

    /**
     * @return string
     */
    public function getLoanAccountTemplate(): string
    {
        return '      <loanaccount>' . PHP_EOL .
            '         <accountnumber>_accountNumber</accountnumber>' . PHP_EOL .
            '         <status>APPROVED</status>' . PHP_EOL .
            '         <due>' . PHP_EOL .
            '            <amount>_amount</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '         </due>' . PHP_EOL .
            '         <duedate>_dueDate</duedate>' . PHP_EOL .
            '         <loantype>PERSONAL</loantype>' . PHP_EOL .
            '         <interest>0.0</interest>' . PHP_EOL .
            '         <extension>' . PHP_EOL .
            '           <BankWallet>WKD-PERSONAL</BankWallet>' . PHP_EOL .
            '         </extension>' . PHP_EOL .
            '      </loanaccount>';
    }
}
