<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CreditLimitService;
use App\Exceptions\CreditLimitException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class CreditLimitController extends Controller
{
    public function __construct(
        private CreditLimitService $creditLimitService
    ) {}

    /**
     * Get loan history for a customer
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getLoanHistory(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string|min:10|max:15'
        ]);

        try {
            $loanHistory = $this->creditLimitService->getLoanHistory(
                $request->phone_number
            );

            return response()->json([
                'success' => true,
                'message' => 'Loan history retrieved successfully',
                'data' => $loanHistory
            ]);

        } catch (CreditLimitException $e) {
            Log::error('CreditLimit API Error', [
                'phone_number' => $request->phone_number,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve loan history',
                'error' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Unexpected error in CreditLimit API', [
                'phone_number' => $request->phone_number,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred',
                'error' => 'Internal server error'
            ], 500);
        }
    }
}
