<?php

namespace App\Http\Controllers\Api;

use App\Jobs\Payments\ProcessAirtelCallback;
use Exception;
use Carbon\Carbon;
use App\Models\Loan;
use App\Models\LoanFee;
use App\Models\Partner;
use App\Models\Customer;
use App\Models\LoanProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Models\LoanApplication;
use App\Models\LoanProductTerm;
use App\Models\LoanProductType;
use App\Models\Accounts\Account;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\Account\AccountSeederService;
use App\Http\Resources\LoanProductsApiResource;
use App\Http\Resources\LoanScheduleApiResource;
use App\Http\Resources\LoanRepaymentApiResource;

class LoansApiController extends Controller
{
    const HARDCODED_LOAN_LIMIT_AMOUNT = 50000;

    /**
     * Return the current balance of a customer's loan.
     *
     * This API endpoint expects a phone number and a partner code in the headers.
     * The partner code is required to authenticate the request. The phone number
     * is used to identify the customer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function loanBalance(Request $request)
    {
        $phone = $request->phone;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            $loan = $customer->loans()->latest()->first();
            if (
                !$loan ||
                $loan->Credit_Application_Status != Loan::APPROVED_STATUS &&
                $loan->Credit_Application_Status != Loan::ARREAR_STATUS
            ) {
                return response()->json([
                    'message' => 'Customer doesn\'t have an active loan.'
                ], 400);
            }
            return response()->json([
                'balance' => $loan->totalOutstandingBalance()
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    /**
     * Return a list of loan products that the customer is eligible for.
     *
     * This API endpoint expects a phone number and a partner code in the headers.
     * The partner code is required to authenticate the request. The phone number
     * is used to identify the customer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function loanProducts(Request $request)
    {
        $phone = $request->phone;
        $loan_products = LoanProduct::withoutGlobalScopes()
            ->with([
                "loan_product_terms",
                "fees",
                "partner",
            ])->get();
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            // Not required here, but maybe for future tracking purposes.
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            return response()->json([
                'loan_products' => LoanProductsApiResource::collection($loan_products)
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    /**
     * Return the ledger for the customer's latest loan.
     *
     * This API endpoint expects a phone number and a partner code in the headers.
     * The partner code is required to authenticate the request. The phone number
     * is used to identify the customer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function loanLedger(Request $request)
    {
        $phone = $request->phone;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            $loan = $customer->loans()
                ->where('Credit_Application_Status', 'Approved')
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception('Customer doesn\'t have an active loan.', 400);
            }

            return response()->json([
                'ledger' => LoanRepaymentApiResource::collection($loan->payments)
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    /**
     * Handle a loan application.
     *
     * This API endpoint expects a phone number, loan product code, loan product term code, amount, loan purpose,
     * number of installments, and frequency of installments in the request body.
     * The partner code is required to authenticate the request. The phone number
     * is used to identify the customer.
     *
     * @return \Illuminate\Http\Response
     */
    public function loanApplication(Request $request)
    {
        $phone = $request->phone;
        $amount = $request->amount;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();

        DB::beginTransaction();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an invalid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan_product = LoanProduct::where('Code', $request->loan_product_code)
                ->first();
            $loan_product_term = LoanProductTerm::where('Code', $request->loan_product_term_code)
                ->first();

            if (!$loan_product) {
                throw new Exception('LoanProduct with the given code was not found.', 400);
            }

            if (!$loan_product_term) {
                throw new Exception('Loan product term with the given code was not found.', 400);
            }

            if (!empty(json_decode($loan_product->Whitelisted_Customers)) && !in_array($customer->Telephone_Number, json_decode($loan_product->Whitelisted_Customers) ?? [])) {
                throw new Exception('Customer is not whitelisted for this loan product.', 400);
            }

            $loans = $customer->loans()
                ->whereNull("Credit_Account_Closure_Date")
                ->whereNull("Credit_Account_Closure_Reason")
                ->latest()
                ->get();

            if ($loans->count() > 0) {
                throw new Exception('Customer already has an active loan. Please clear your outstanding loan of amount ' . $loans->first()->totalOutstandingBalance() . '.', 400);
            }

            // TODO Najja update this logic
            if ($amount > self::HARDCODED_LOAN_LIMIT_AMOUNT) {
                $elegible_loan_limit = number_format(self::HARDCODED_LOAN_LIMIT_AMOUNT);
                throw new Exception('Requested amount cannot be greater than your eligible loan limit of ' . $elegible_loan_limit . '.', 400);
            }

            $disbursement_ova = Account::where('Partner_ID', $partner->id)
                ->where("slug", AccountSeederService::DISBURSEMENT_OVA_SLUG)
                ->first();

            if ($disbursement_ova->balance < $amount) {
                throw new Exception('Insufficient funds in Disbursement OVA account.', 400);
            }

            $credit_account_type = LoanProductType::where("name", "Mobile Loan")->first();
            // Create a new loan application
            $loan_application = LoanApplication::create([
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Loan_Product_ID' => $loan_product->id,
                'Loan_Purpose' => $request->loan_purpose,
                'Applicant_Classification' => $customer->Classification,
                'Credit_Application_Date' => Carbon::now(),
                'Amount' => $amount,
                'Credit_Application_Status' => 'Approved',
                'Credit_Account_or_Loan_Product_Type' => $credit_account_type->Code,
                'Credit_Application_Duration' => '0', // time between application and the time it is approved or rejected. This is auto so zero(0)
                'Client_Consent_flag' => 'Yes',
                'Country' => $customer->Country,
                'District' => $customer->District,
                'Subcounty' => $customer->Subcounty,
                'Parish' => $customer->Parish,
                'Village' => $customer->Village,
                'Last_Status_Change_Date' => now(),
                'Credit_Amount_Approved' => $amount
            ]);

            // Create a new loan associated with the application
            $loan = Loan::create([
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Loan_Product_ID' => $loan_product->id,
                'Loan_Application_ID' => $loan_application->id,
                'Credit_Application_Status' => 'Approved', // You can dynamically adjust this based on approval logic
                'Credit_Account_Reference' => Loan::generateReference(),
                'Credit_Account_Date' => Carbon::now(),
                'Credit_Amount' => $amount,
                'Facility_Amount_Granted' => $amount,
                'Credit_Amount_Drawdown' => '0.00',
                'Credit_Account_Type' => $credit_account_type->Code,
                'Currency' => 'UGX',
                'Maturity_Date' => Loan::determineMaturityDate($request->number_of_installments, $request->frequency_of_installments, $request->number_of_installments),
                'Annual_Interest_Rate_at_Disbursement' => $loan_product_term->Interest_Rate,
                'Credit_Amortization_Type' => 1, // Refer to the DSM APPENDIX 1.11
                'Credit_Payment_Frequency' => $request->frequency_of_installments,
                'Number_of_Payments' => $request->number_of_installments,
                'Client_Advice_Notice_Flag' => 'Yes',
                'Term' => $loan_product_term->Value,
                'Type_of_Interest' => 1, // Refer to the DSM APPENDIX 1.9 0-Fixed, 1-Floating
                'Client_Consent_Flag' => 'Yes',
                'Date_of_First_Payment' => Loan::determineDateOfFirstPayment($request->number_of_installments, $request->frequency_of_installments),
                'Interest_Rate' => $loan_product_term->Interest_Rate,
                'Interest_Calculation_Method' => $loan_product_term->Interest_Calculation_Method,
                'Loan_Term_ID' => $loan_product_term->id,
            ]);

            // Apply all the fees. Save it.
            $fees = $loan_product->fees;
            foreach ($fees as $fee) {
                $Value = $fee->Value;
                $Calculation_Method = $fee->Calculation_Method;
                $Applicable_On = $fee->Applicable_On;
                $Applicable_At = $fee->Applicable_At;

                $Amount = $Value;
                if ($Calculation_Method == "Percentage") {

                    if ($Applicable_On == "Principal") {
                        $principal = $loan->totalPrincipal();
                        $Amount = $principal * $Value / 100;
                    }

                    if ($Applicable_On == "Interest") {
                        $interest = $loan->totalInterest();
                        $Amount = $interest * $Value / 100;
                    }

                    if ($Applicable_On == "Balance") {
                        $principal = $loan->totalPrincipal();
                        $interest = $loan->totalInterest();
                        $balance = $principal + $interest;
                        $Amount = $balance * $Value / 100;
                    }
                }

                LoanFee::create([
                    'Partner_ID' => $partner->id,
                    'Loan_Product_ID' => $loan_product->id,
                    'Loan_Product_Fee_ID' => $fee->id,
                    'Amount_To_Pay' => $Amount,
                    'Amount' => $Amount,
                    'Charge_At' => "Repayment",
                    // 'Charge_At' => $Applicable_At,
                    'Customer_ID' => $customer->id,
                    'Loan_ID' => $loan->id,
                ]);
            }

            // Charge the fee that are to be applied at disbursement.
            // Send the customer less amount but record the full principal and interest
            // in the loan account.
            // $loan_fees = LoanFee::where('Loan_ID', $loan->id)
            //     ->where('Charge_At', 'Disbursement')
            //     ->where('Customer_ID', $customer->id)
            //     ->get();

            // foreach ($loan_fees as $fee) {
            //     $fee->affectAccounts();
            // }

            // $deductible_fees_at_disbursement = $loan_fees->sum('Amount');

            // TODO MA
            // Send money to the customer on their phone number or the designated channels
            // Deduct the fees from the loan amount to be send to the user.
            //-// $amount_to_send_to_customer = $amount - $deductible_fees_at_disbursement;
            // Do all the above only after the external request has passed or the DB::transaction also takes care of
            // this issues by only committing the changes if the external request is successful.
            DB::commit();
            return response()->json([
                'message' => "Your loan request of UGX $amount was initiated successfully. Please wait for SMS confirmation",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    public function loanElegibility(Request $request)
    {
        $phone = $request->phone;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            // TODO MA
            // Run the Decision engine here.

            // Hardcode value for now
            $amount = number_format(self::HARDCODED_LOAN_LIMIT_AMOUNT);
            return response()->json([
                'message' => "Congratulations! You are eligible for a loan of UGX $amount.",
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    public function loanRepayment(Request $request)
    {
        $phone = $request->phone;
        $amount = $request->amount;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        DB::beginTransaction();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = $customer->loans()
                ->where('Credit_Application_Status', 'Approved')
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception("Customer doesn't have an active loan.", 400);
            }

            if ($loan->Status == Loan::WRITTEN_OFF_STATUS) {
                throw new Exception('You cannot make a repayment on a written off loan.', 400);
            }

            $outstanding_balance = $loan->totalOutstandingBalance();

            if ($loan->isCleared()) {
                throw new Exception("You cannot make a repayment on a cleared loan. You don't have any active loan.", 400);
            }

            if ($amount > $outstanding_balance) {
                throw new Exception("You cannot make a repayment greater than the outstanding balance of UGX $outstanding_balance.", 400);
            }

            // TODO MA
            // Send the pin to the customer on their phone number to debit the money.
            // Do this first before recording the payment below.

            // TODO MA - Commented out for debugging purposes because payments are being duplicated. Anyone Undo this after 31/01/2025.
            // $loan_payment = LoanRepayment::createPayment($loan, $amount);
            // $loan_payment->affectAccounts();

            DB::commit();
            return response()->json([
                'message' => "Your payment of $amount was initiated successfully."
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    public function loanSchedule(Request $request)
    {
        $phone = $request->phone;
        $partner_code = $request->header('X-PARTNER-CODE');
        $partner = Partner::where('Identification_Code', $partner_code)->first();
        $customer = Customer::where('Telephone_Number', $phone)->first();
        try {
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = $customer->loans()
                ->where('Credit_Application_Status', 'Approved')
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception("Customer doesn't have an active loan.", 400);
            }

            $loan_schedules = $loan->schedule()->get();
            return response()->json([
                'loan_schedules' => LoanScheduleApiResource::collection($loan_schedules)
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    public function airtelTransactionCallback(Request $request, $PartnerID): \Illuminate\Http\JsonResponse
    {
        $details = [
            'PartnerID' => $PartnerID,
            'Name' => $request->name,
            'Message' => 'Success',
            'data' => $request->all()
        ];

        Log::info('Airtel Callback: ' . json_encode($details));

        ProcessAirtelCallback::dispatch($details);

        return response()->json($details);
    }

    public function mtnTransactionCallback(Request $request, $PartnerID)
    {

        $details = [
            'PartnerID' => $PartnerID,
            'Name' => $request->name,
            'Message' => 'Reached the LMS successfully',
            'data' => $request->all()
        ];

        //ProcessAirtelCallback::dispatch($details);

        return response()->json($details);
    }
}
