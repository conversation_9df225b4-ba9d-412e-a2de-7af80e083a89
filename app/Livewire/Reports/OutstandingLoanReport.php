<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetOutstandingLoanReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\OutstandingLoanExport;

class OutstandingLoanReport extends Component
{
    use WithPagination, ExportsData;

    public bool $includeWrittenOffLoans = false;

    public function mount()
    {
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.outstanding-loans-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        $filters = $this->getFilters();
        $filters['withWrittenOffLoans'] = $this->includeWrittenOffLoans;

        return app(PdfGeneratorService::class)
            ->view('pdf.outstanding-loans', [
                'records' => app(GetOutstandingLoanReportDetailsAction::class)
                    ->filters($filters)
                    ->execute(),
                'partnerName' => auth()->user()->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters()
            ])
            ->streamFromLivewire();
    }

    public function excelExport()
    {
        $filters = $this->getFilters();
        $filters['withWrittenOffLoans'] = $this->includeWrittenOffLoans;
        return Excel::download(new OutstandingLoanExport($filters), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate)) {
            return collect();
        }

        $filters = $this->getFilters();
        $filters['withWrittenOffLoans'] = $this->includeWrittenOffLoans;

        return app(GetOutstandingLoanReportDetailsAction::class)
            ->paginate()
            ->filters($filters)
            ->execute();
    }
}
