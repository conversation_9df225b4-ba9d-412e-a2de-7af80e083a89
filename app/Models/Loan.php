<?php

namespace App\Models;

use App\Enums\CreditPaymentFrequency;
use App\Enums\LoanAccountType;
use App\Traits\HasStatuses;
use Exception;
use App\Models\Scopes\PartnerScope;
use App\Notifications\SmsNotification;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Loan extends Model
{
    use HasFactory, SoftDeletes, HasStatuses;

    const APPROVED_STATUS = "Approved";
    const WRITTEN_OFF_STATUS = "Written-off";
    const ARREAR_STATUS = "In-Arrears";

    const ACCOUNT_STATUS_FULLY_PAID_OFF = 4;
    const ACCOUNT_STATUS_WRITTEN_OFF = 3;
    const ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS = 5;
    const ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS = 1;

    const SUPPORTED_Credit_Account_Statuses = [
        self::ACCOUNT_STATUS_FULLY_PAID_OFF => "Fully Paid",
        self::ACCOUNT_STATUS_WRITTEN_OFF => "Written-off",
        self::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS => "Within Terms",
        self::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS => "Outstanding and Beyond Terms",
        null => ""
    ];

    protected $fillable = [
        'Partner_ID',
        'Customer_ID',
        'Loan_Product_ID',
        'Loan_Application_ID',
        'Credit_Application_Status',
        'Credit_Account_Reference',
        'Credit_Account_Date',
        'Credit_Amount',
        'Facility_Amount_Granted',
        'Credit_Amount_Drawdown',
        'Credit_Account_Type',
        'Currency',
        'Maturity_Date',
        'Annual_Interest_Rate_at_Disbursement',
        'Date_of_First_Payment',
        'Credit_Amortization_Type',
        'Credit_Payment_Frequency',
        'Number_of_Payments',
        'Instalment_Amount',
        'Client_Advice_Notice_Flag',
        'Term',
        'Type_of_Interest',
        'Loan_Term_ID',
        'Interest_Rate',
        'Interest_Calculation_Method',
        'Credit_Account_Status',
        'Can_Restructure',
        'Number_Of_Restructures',
        'Last_Restructure_Date',
        'Last_Status_Change_Date',
        'Credit_Account_Closure_Date',
        'Credit_Account_Closure_Reason',
        'Credit_Account_Closure_Officer',
        'Written_Off_Date',
        'Written_Off_Amount',
        'Written_Off_Reason',
        'Written_Off_Officer',
        'Written_Off_Amount_Recovered',
        'Last_Recovered_At',
        'Blacklisted_Date',
        'Blacklisted_Reason',
        'Blacklisted_By',
        'Auto_Sweep_Started',
        'Auto_Sweep_Ended',
    ];

    protected function casts(): array
    {
        return [
            'Last_Status_Change_Date' => 'datetime',
            'Last_Restructure_Date' => 'datetime',
            'Can_Restructure' => 'boolean',
            'Maturity_Date' => 'datetime',
            'Credit_Account_Date' => 'datetime',
            'Date_of_First_Payment' => 'datetime',
            'Credit_Account_Closure_Date' => 'datetime',
            'Written_Off_Date' => 'date',
            'Last_Recovered_At' => 'datetime',
            'Blacklisted_Date' => 'datetime',
            'Auto_Sweep_Started' => 'datetime',
            'Auto_Sweep_Ended' => 'datetime',
        ];
    }

    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope(new PartnerScope);

        static::created(function (Loan $loan) {
            DB::beginTransaction();
            $disbursement = LoanDisbursement::createDisbursement($loan);
            $application = $loan->loan_application;
            $application->update(['Credit_Application_Status' => 'Approved']);
            try {
                self::createLoanFees($loan);
                LoanSchedule::generateSchedule($loan);
                $disbursement->affectAccounts();
                $interestRate = $loan->Interest_Rate;
                $productName = $loan->loan_product->name;
                $customer = $loan->customer;
                $message = 'Congratulations ' . $customer->name . ', your loan of UGX ' . number_format($loan->Credit_Amount) . ' ' . $productName . ' from ' . $loan->partner->Institution_Name . ' at ' . $interestRate . '% interest has been approved. Dial ' . $loan->loan_product->ussdCode() . ' to repay by ' . $loan->Maturity_Date->toDateString() . ' to avoid late fees';
                $customer->notify(new SmsNotification($message, $customer->Telephone_Number, $customer->id, $loan->Partner_ID));

                DB::commit();
                return response()->json(['returnMessage' => 'Loan disbursed successfully', 'returnCode' => 200]);
            } catch (Exception $e) {
                DB::rollBack();
                Log::error('Loan creation failed', [
                    'loan_id' => $loan->id,
                    'error' => $e->getMessage(),
                ]);
                return response()->json(['returnMessage' => $e->getMessage(), 'returnCode' => 400]);
            }
        });
    }

    public function scopeAgeingCategories(Builder $query, string $endDate, Collection $provisions): Builder
    {
        if ($provisions->isEmpty() || $provisions->count() !== 5) {
            return $query->addSelect($this->getDefaultAgeingCategories($endDate));
        }

        return $query->addSelect($this->getCustomAgeingCategories($endDate, $provisions));
    }

    /**
     * Get the default ageing categories.
     *
     * @param string $endDate
     * @return array
     */
    private function getDefaultAgeingCategories(string $endDate): array
    {
        return [
            'principal_in_arrears' => $this->buildPrincipalInArrearsQuery($endDate),
            'days_in_arrears' => $this->buildDaysInArrearsQuery($endDate),
            'principal_outstanding_at_30' => $this->buildPrincipalOutstandingQuery($endDate, 30, 0),
            'principal_outstanding_at_60' => $this->buildPrincipalOutstandingQuery($endDate, 60, 31),
            'principal_outstanding_at_90' => $this->buildPrincipalOutstandingQuery($endDate, 90, 61),
            'principal_outstanding_at_180' => $this->buildPrincipalOutstandingQuery($endDate, 180, 91),
            'principal_outstanding_after_180' => $this->buildPrincipalOutstandingQuery($endDate, 0, 181),
        ];
    }

    /**
     * Get custom ageing categories based on provisions.
     *
     * @param string $endDate
     * @param Collection $provisions
     * @return array
     */
    private function getCustomAgeingCategories(string $endDate, Collection $provisions): array
    {
        $classifications = [
            'principal_outstanding_at_30',
            'principal_outstanding_at_60',
            'principal_outstanding_at_90',
            'principal_outstanding_at_180',
            'principal_outstanding_after_180',
        ];

        $customizedAgeing = $provisions->mapWithKeys(function ($provision, $key) use ($classifications, $endDate) {
            return [
                $classifications[$key] => $this->buildPrincipalOutstandingQuery(
                    $endDate,
                    $provision->maximum_days,
                    $provision->minimum_days
                )
            ];
        })->all();

        // Add common queries
        $customizedAgeing['principal_in_arrears'] = $this->buildPrincipalInArrearsQuery($endDate);
        $customizedAgeing['days_in_arrears'] = $this->buildDaysInArrearsQuery($endDate);

        return $customizedAgeing;
    }

    /**
     * Build a query for principal in arrears.
     *
     * @param string $endDate
     * @return Builder
     */
    private function buildPrincipalInArrearsQuery(string $endDate): \Illuminate\Database\Eloquent\Builder
    {
        return LoanSchedule::query()
            ->selectRaw('sum(principal_remaining)')
            ->whereColumn('loan_id', 'loans.id')
            ->where('total_outstanding', '>', 0)
            ->whereDate('payment_due_date', '<', $endDate)
            ->limit(1);
    }

    /**
     * Build a query for days in arrears.
     *
     * @param string $endDate
     * @return Builder
     */
    private function buildDaysInArrearsQuery(string $endDate): \Illuminate\Database\Eloquent\Builder
    {
        return LoanSchedule::query()
            ->selectRaw('datediff(payment_due_date, ?)', [$endDate])
            ->whereColumn('loan_id', 'loans.id')
            ->where('total_outstanding', '>', 0)
            ->whereDate('payment_due_date', '<', $endDate)
            ->orderBy('payment_due_date')
            ->limit(1);
    }

    /**
     * Build a query for principal outstanding within a date range.
     *
     * @param string $endDate
     * @param int $maxDays
     * @param int $minDays
     * @return Builder
     */
    private function buildPrincipalOutstandingQuery(string $endDate, int $maxDays, int $minDays): Builder
    {
        $query = LoanSchedule::query()
            ->selectRaw('sum(principal_remaining)')
            ->whereColumn('loan_id', 'loans.id')
            ->where('principal_remaining', '>', 0)
            ->whereDate('payment_due_date', '<', $endDate);

        if ($minDays === 0) {
            // Special case for 0-30 days range
            $query->whereBetween('payment_due_date', [
                Carbon::parse($endDate)->subDays($maxDays)->format('Y-m-d'),
                $endDate
            ]);
        } elseif ($maxDays === 0) {
            $query->whereDate('payment_due_date', '<=', Carbon::parse($endDate)->subDays($minDays)->format('Y-m-d'));
        } else {
            $query->whereBetween('payment_due_date', [
                Carbon::parse($endDate)->subDays($maxDays)->format('Y-m-d'),
                Carbon::parse($endDate)->subDays($minDays)->format('Y-m-d')
            ]);
        }

        return $query->limit(1);
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class, 'Partner_ID');
    }

    public function loan_product()
    {
        return $this->belongsTo(LoanProduct::class, 'Loan_Product_ID');
    }

    public function product()
    {
        return $this->belongsTo(LoanProduct::class, 'Loan_Product_ID');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'Customer_ID');
    }

    public function loan_application()
    {
        return $this->belongsTo(LoanApplication::class, 'Loan_Application_ID');
    }

    public function application()
    {
        return $this->belongsTo(LoanApplication::class, 'Loan_Application_ID');
    }

    public function loan_term()
    {
        return $this->belongsTo(LoanProductTerm::class, 'Loan_Term_ID');
    }

    public function term(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(LoanProductTerm::class, 'Loan_Term_ID');
    }

    public function loan_repayments(): HasMany
    {
        return $this->hasMany(LoanRepayment::class);
    }

    public function schedule(): HasMany
    {
        return $this->hasMany(LoanSchedule::class);
    }

    public function loan_schedules(): HasMany
    {
        return $this->hasMany(LoanSchedule::class);
    }

    public function loan_repayment_schedule(): HasMany
    {
        return $this->hasMany(LoanSchedule::class);
    }

    public function assetPrincipalPayments(): HasMany
    {
        return $this->hasMany(AssetLoanPrincipalPayment::class);
    }

    public function latestOutstandingPayment(): HasOne
    {
        return $this->hasOne(LoanSchedule::class)
            ->ofMany(['payment_due_date' => 'min'], function ($query) {
                $query->where('total_outstanding', '>', 0);
            });
    }

    public function lastRepayment(): HasOne
    {
        return $this->hasOne(LoanRepayment::class)
            ->ofMany('Last_Payment_Date', 'max');
    }

    public function expiryDate()
    {
        return $this->schedule()->max('payment_due_date');
    }

    public function disbursement(): HasOne
    {
        return $this->hasOne(LoanDisbursement::class);
    }

    public function loan_disbursement(): HasOne
    {
        return $this->hasOne(LoanDisbursement::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(LoanRepayment::class);
    }

    public function writtenOffBy(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'Written_Off_Officer', 'id');
    }

    public static function generateReference()
    {
        return "GG-" . strtoupper(uniqid());
    }

    public static function determineMaturityDate($number_of_installments, $frequency_of_installments, $loanTermInDays)
    {
        if (config('lms.loans.enable_ageing')) {
            $current_date = now()->subDays(config('lms.loans.back_date_days'));
        } else {
            $current_date = now();
        }

        switch ($frequency_of_installments) {
            case 'Once':
                $maturity_date = $current_date->startOfDay()->addDays($loanTermInDays);
                break;
            case 'Daily':
                $maturity_date = $current_date->startOfDay()->addDays($number_of_installments);
                break;
            case 'DailyExcludingSundays':
                // Start counting days excluding Sundays
                $maturity_date = $current_date->startOfDay()->addDays(7);
                $daysAdded = 0;
                while ($daysAdded < $number_of_installments) {
                    $maturity_date->addDay();
                    // Only count non-Sundays
                    if ($maturity_date->dayOfWeek !== Carbon::SUNDAY) {
                        $daysAdded++;
                    }
                }
                break;
            case 'Weekly':
                $maturity_date = $current_date->startOfDay()->addWeeks($number_of_installments);
                break;
            case 'Bi-weekly':
                $maturity_date = $current_date->startOfDay()->addWeeks($number_of_installments * 2);
                break;
            case 'Monthly':
                $maturity_date = $current_date->startOfDay()->addMonths($number_of_installments);
                break;
            default:
                throw new Exception("Invalid frequency '$frequency_of_installments' of installments");
        }
        return $maturity_date;
    }

    public static function determineDateOfFirstPayment($frequency_of_installments, $loanTermInDays)
    {
        $disbursement_date = now();
        switch ($frequency_of_installments) {
            case 'Once':
                $first_payment_date = $disbursement_date->startOfDay()->addDays($loanTermInDays);
                break;
            case 'Daily':
                $first_payment_date = $disbursement_date->startOfDay()->addDay();
                break;
            case 'DailyExcludingSundays':
                $first_payment_date = $disbursement_date->startOfDay()->addDay(7);
                break;
            case 'Weekly':
                $first_payment_date = $disbursement_date->startOfDay()->addWeek();
                break;
            case 'Bi-weekly':
                $first_payment_date = $disbursement_date->startOfDay()->addWeeks(2);
                break;
            case 'Monthly':
                $first_payment_date = $disbursement_date->startOfDay()->addMonth();
                break;
            default:
                throw new Exception('Invalid frequency of installments');
        }
        return $first_payment_date;
    }

    public function getOutstandingFees()
    {
        return $this->schedule->lazy()->filter(function ($schedule) {
            return str($schedule->type)->contains('fee', true);
        })->sum('total_outstanding');
    }

    public function getOutstandingPenalties()
    {
        $lazyPenalties = $this->penalties->lazy();
        $total_amount = $lazyPenalties->where('Status', '<>', LoanPenalty::FULLY_PAID)->sum('Amount_To_Pay');
        $amount_paid = $lazyPenalties->where('Status', '<>', LoanPenalty::FULLY_PAID)->sum('Amount');
        return $total_amount - $amount_paid;
    }

    public function fees()
    {
        return $this->hasMany(LoanFee::class, 'Loan_ID');
    }

    public function written_off_loan(): HasOne
    {
        return $this->hasOne(WrittenOffLoan::class, 'Loan_ID');
    }

    public function penalties()
    {
        return $this->hasMany(LoanPenalty::class, 'Loan_ID');
    }

    public function totalInterest()
    {
        return $this->schedule->sum('interest');
    }

    public function totalPrincipal()
    {
        return $this->schedule->sum('principal');
    }

    public function totalFees()
    {
        return $this->schedule()->where('type', 'like', '%fee%')->lazy()->sum('total_payment');
    }

    public function totalPenalties()
    {
        return $this->penalties->sum('Amount_To_Pay');
    }

    public function penaltiesPaid()
    {
        return $this->totalPenalties() - $this->getOutstandingPenalties();
    }

    public function penaltiesReceivable()
    {
        $loanId = $this->id;
        $loanPenaltyIds = LoanPenalty::where('Loan_ID', $loanId)->pluck('id');
        return JournalEntry::where('account_name', 'Penalties Receivables')
            ->where('transactable', LoanPenalty::class)
            ->whereIn('transactable_id', $loanPenaltyIds)
            ->sum('amount');
    }

    public function totalAmountToPay()
    {
        return $this->totalPrincipal() + $this->totalInterest() + $this->totalPenalties();
    }

    public function totalPayment()
    {
        return $this->loan_repayments()->sum('amount');
    }

    public function dailyPayment()
    {
        return $this->totalRepayment() / $this->Number_of_Payments;
    }

    public function totalRepayment()
    {
        return $this->totalPayment();
    }

    public function totalToBePaid()
    {
        return $this->schedule->lazy()->sum('total_outstanding');
    }

    public function totalOutstandingBalance()
    {
        // return $this->totalToBePaid() + $this->getOutstandingPenalties(); // put back if it backfires
        if ($this->isWrittenOff()) {
            return $this->Written_Off_Amount - $this->Written_Off_Amount_Recovered;
        }
        return $this->getOutstandingPrincipal() + $this->interestDue() + $this->feesDue() + $this->getOutstandingPenalties();
    }

    public function getOutstandingInterest()
    {
        return $this->schedule->lazy()->sum('interest_remaining');
    }

    public function interestDue()
    {
        // Get the first installment
        $firstInterests = $this->schedule()
            ->where('installment_number', 1)
            ->whereNotNull('interest_remaining')
            ->select('id')
            ->get();
        $firstInterestIds = $firstInterests->pluck('id');


        $firstInterestDetails = $this->schedule()
            ->whereIn('id', $firstInterestIds)
            ->get();

        $firstInterestsTotal = $firstInterestDetails->sum('interest_remaining');

        // Get the sum of interest for installments where payment_due_date is <= now()
        // and exclude the first installment
        $endDate = now();
        if ($this->isAssetLoan()) {
            $customerAsset = CustomerAsset::query()
                ->where('Loan_ID', $this->id)->first();
            if ($customerAsset) {
                if ($customerAsset->Status == 'Reposessed') {
                    $endDate = $customerAsset->Reposessed_Date;
                }
            }
        }

        $interestDue = $this->schedule()
            ->where('payment_due_date', '<=', $endDate)
            ->whereNotIn('id', $firstInterestIds) // Exclude firstFeeIds
            ->sum('interest_remaining');
        // Ensure the first installment's interest is included
        return $interestDue + $firstInterestsTotal;
    }

    public function principalDue()
    {
        return $this->schedule
            ->where('payment_due_date', '<=', now())
            ->lazy()
            ->sum('principal_remaining');
    }

    public function feesDue()
    {
        // Get the first fee for each installment
        $firstFees = $this->schedule()
            ->where('type', 'like', '%fee%')
            ->where('installment_number', 1)
            ->select('id')
            ->get();
        // Extract the IDs of the first fees
        $firstFeeIds = $firstFees->pluck('id');
        // Retrieve the full details of the first fees
        $firstFeeDetails = $this->schedule()
            ->whereIn('id', $firstFeeIds)
            ->get();

        // Sum the total_outstanding for the first fees
        $firstFeesTotal = $firstFeeDetails->sum('total_outstanding');
        // Get the sum of fees where payment_due_date is <= now()
        $feesDue = $this->schedule()
            ->where('type', 'like', '%fee%')
            ->where('payment_due_date', '<=', now())
            ->whereNotIn('id', $firstFeeIds) // Exclude firstFeeIds
            ->sum('total_outstanding');
        // Ensure the first fees are included

        return $feesDue + $firstFeesTotal;
    }

    public function getOutstandingPrincipal()
    {
        return $this->schedule->sum('principal_remaining');
    }

    public function isRecurring()
    {
        return $this->Number_of_Payments > 1;
    }

    public function isOverdue()
    {
        return $this->Credit_Account_Status == self::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS;
    }

    public function isCleared()
    {
        return $this->totalOutstandingBalance() == 0;
    }

    public function isWrittenOff(): bool
    {
        return $this->Credit_Account_Status === self::ACCOUNT_STATUS_WRITTEN_OFF;
    }

    public function canWriteOff(): bool
    {
        return $this->Maturity_Date->isBefore(now()) && in_array($this->Credit_Account_Status, [
            LoanAccountType::BeyondTerms->value,
            LoanAccountType::WithinTerms->value,
        ]) && $this->isCleared() === false;
    }

    public function getStatusAttribute()
    {
        return self::SUPPORTED_Credit_Account_Statuses[$this->Credit_Account_Status];
    }

    public static function determineNumberOfPayments($loanTerm, $frequencyOfPayment): int
    {
        switch ($frequencyOfPayment) {
            case "Once":
                return 1;
            case "Daily":
                return $loanTerm; // Assuming loan term is in days
            case "DailyExcludingSundays":
                // Calculate number of Sundays in the loan period
                $startDate = new DateTime(); // assuming loan starts today
                $endDate = clone $startDate;
                $endDate->add(new DateInterval("P{$loanTerm}D"));

                $sundays = 0;
                $interval = new DateInterval('P1D');
                $period = new DatePeriod($startDate, $interval, $endDate);

                foreach ($period as $day) {
                    if ($day->format('w') == 0) { // 0 = Sunday
                        $sundays++;
                    }
                }
                return $loanTerm - $sundays;
            case "Weekly":
                return floor($loanTerm / 7);
            case "Bi-weekly":
                return floor($loanTerm / 14);
            case "Monthly":
                return floor($loanTerm / 30);
            default:
                throw new Exception("Invalid repayment frequency: $frequencyOfPayment");
        }
    }

    public function isAssetLoan()
    {
        return $this->loan_product->isAssetLoan();
    }

    public function addons()
    {
        return $this->schedule()
            ->selectRaw('type, SUM(principal) as total_principal, SUM(interest) as total_interest, SUM(total_payment) as total_payment')
            ->whereNot('type', 'Loan')
            ->whereNot('type', 'Monitoring Fees')
            ->whereNot('type', 'Collection Fees')
            ->groupBy('type')
            ->get();
    }

    public function getAddonsAttribute()
    {
        return $this->addons();
    }

    public function downpayment()
    {
        return Transaction::where('Loan_ID', $this->id)
            ->where('Type', 'Downpayment')
            ->first();
    }

    public function loanSweeps()
    {
        return $this->hasMany(LoanSweep::class);
    }

    public function asset()
    {
        return $this->hasOne(CustomerAsset::class, 'Loan_ID', 'id');
    }

    public function getDownpaymentAttribute()
    {
        return $this->downpayment();
    }

    public function productPenalties()
    {
        return $this->product->penalties;
    }

    public function getOutstandingDays()
    {
        $lastUnPaidDate = $this->schedule()
            ->where('payment_due_date', '<', Carbon::parse(now()))
            ->where('total_outstanding', '>', 0)->min('payment_due_date');
        $minDate = Carbon::parse(now());
        $lastUnPaidDate = Carbon::parse($lastUnPaidDate);
        return round($lastUnPaidDate->diffInDays($minDate));
    }

    public function getAmountDue()
    {
        $today = date('Y-m-d');
        return $this->schedule()
            ->where('payment_due_date', '<=', $today)
            ->sum('total_outstanding');
    }

    public function dueDate()
    {
        return $this->schedule()
            ->where('total_outstanding', '>', 0)
            ->min('payment_due_date');
    }

    public function daysOverdue()
    {
        $maxDate = Carbon::parse(now());
        $minDate = Carbon::parse($this->dueDate());
        return round($minDate->diffInDays($maxDate));
    }

    public function overDueAmount()
    {
        $today = date('Y-m-d');
        return $this->schedule()
            ->where('payment_due_date', '<', $today)
            ->where('total_outstanding', '>', 0)
            ->sum('total_outstanding');
    }

    public function totalPrincipalPaid()
    {

        return $this->totalPrincipal() - $this->getOutstandingPrincipal();
    }

    public function totalInterestPaid()
    {

        return $this->totalInterest() - $this->getOutstandingInterest();
    }

    public function totalPenaltiesPaid()
    {

        return $this->totalPenalties() - $this->getOutstandingPenalties();
    }

    public function getDailyRepayment()
    {
        if ($this->Number_of_Payments == 0) {
            return 0;
        }

        return $this->totalToBePaid() / $this->Number_of_Payments;
    }


    /**
     * Create loan fees for the given loan.
     */
    public static function createLoanFees(Loan $loan)
    {
        $loanProduct = $loan->product;
        $fees = $loanProduct->fees;
        $numberOfInstallments = $loan->Number_of_Payments;
        $collectionFees = [];

        foreach ($fees as $fee) {
            $Value = $fee->Value;
            $Calculation_Method = $fee->Calculation_Method;
            $Applicable_On = $fee->Applicable_On;
            $Applicable_At = $fee->Applicable_At;
            $Payable_Account_ID = $fee->Payable_Account_ID;

            // Store installment balance fees separately for later calculation
            if ($Applicable_On == 'Installment Balance') {
                $collectionFees[] = $fee;
                continue;
            }
            $Amount = $Value;

            // Calculate based on percentage
            if ($Calculation_Method == "Percentage") {
                if ($Applicable_On == "Principal") {
                    $Amount = $loan->Credit_Amount * ($Value / 100);
                }
                // elseif ($Applicable_On == "Interest") {
                //     $Amount = $loan->totalInterest() * ($Value / 100);
                // } elseif ($Applicable_On == "Balance") {
                //     $Amount = ($loan->totalPrincipal() + $loan->totalInterest()) * ($Value / 100);
                // }
            }

            // Multiply by installments if charged at repayment
            if ($Applicable_At == 'Repayment') {
                $Amount *= $numberOfInstallments;
            }

            LoanFee::create([
                'Partner_ID' => $loan->partner->id,
                'Loan_Product_ID' => $loanProduct->id,
                'Loan_Product_Fee_ID' => $fee->id,
                'Amount_To_Pay' => $Amount,
                'Amount' => ($Applicable_At == 'Application' || $Applicable_At == 'Disbursement') ? $Amount : 0,
                'Charge_At' => $Applicable_At,
                'Payable_Account_ID' => $Payable_Account_ID,
                'Customer_ID' => $loan->customer->id,
                'Loan_ID' => $loan->id,
                'Status' => ($Applicable_At == 'Application' || $Applicable_At == 'Disbursement') ? LoanFee::FULLY_PAID : LoanFee::NOT_PAID
            ]);
        }

        // Second pass: Process installment balance fees
        foreach ($collectionFees as $fee) {
            $feeAmount = $loan->Credit_Amount * ($fee->Value / 100);

            LoanFee::create([
                'Partner_ID' => $loan->partner->id,
                'Loan_Product_ID' => $loanProduct->id,
                'Loan_Product_Fee_ID' => $fee->id,
                'Amount_To_Pay' => $feeAmount,
                'Amount' => 0,
                'Charge_At' => $fee->Applicable_At,
                'Payable_Account_ID' => $Payable_Account_ID,
                'Customer_ID' => $loan->customer->id,
                'Loan_ID' => $loan->id,
            ]);
        }
    }

    /**
     * Calculate details for an add-on.
     *
     * @param object $addon
     * @param float $loanAmount
     * @return array
     */
    public static function calculateAddonDetails($addon, $loanAmount)
    {
        $addonAmount = 0;
        $interestCalculated = 0;
        $downPayment = 0;

        if ($addon->Charge_Interest_On === "On Loan Principal") {
            if ($addon->Interest_Calculation_Method === 'Declining Balance - Discounted') {
                $interestDetails = LoanSchedule::calculateDecliningBalanceTotalRepaymentAndInterest(
                    $loanAmount,
                    $addon->Interest_Rate,
                    $addon->Term,
                    $addon->Repayment_Cycle,
                    $addon->Interest_Cycle
                );
                $interestCalculated = $interestDetails['total_interest'];
            } elseif ($addon->Interest_Calculation_Method == 'Flat') {
                $interestDetails = LoanSchedule::calculateFlatTotalRepaymentAndInterest(
                    $loanAmount,
                    $addon->Interest_Rate,
                    $addon->Term,
                    $addon->Repayment_Cycle,
                    $addon->Interest_Cycle
                );

                $interestCalculated = $interestDetails['total_interest'];
            } elseif ($addon->Interest_Calculation_Method === 'Amortization') {
                $interestDetails = LoanSchedule::calculateAmortizedTotalRepaymentAndInterest(
                    $loanAmount,
                    $addon->Interest_Rate,
                    $addon->Term,
                    $addon->Repayment_Cycle,
                    $addon->Interest_Cycle
                );
                $interestCalculated = $interestDetails['total_interest'];
            }

            $addonAmount = $interestCalculated * $addon->Amount;

            $downPayment = round($interestCalculated * ($addon->Downpayment_Percentage / 100));
        } else {
            $addonAmount = $addon->Amount;
            if ($addon->Interest_Cycle == 'None') {
                $interestCalculated = 0;
                $numberOfPayments = Loan::determineNumberOfPayments($addon->Term, $addon->Repayment_Cycle);
                $totalpayment = $addonAmount * $numberOfPayments;
                $addonAmount = $totalpayment;
            } else {
                if ($addon->Interest_Calculation_Method === 'Declining Balance - Discounted') {
                    $interestDetails = LoanSchedule::calculateDecliningBalanceTotalRepaymentAndInterest(
                        $addonAmount,
                        $addon->Interest_Rate,
                        $addon->Term,
                        $addon->Repayment_Cycle,
                        $addon->Interest_Cycle
                    );
                    $totalpayment = $interestDetails['total_repayment'];
                    $interestCalculated = $interestDetails['total_interest'];
                } elseif ($addon->Interest_Calculation_Method === 'Flat') {
                    $interestDetails = LoanSchedule::calculateFlatTotalRepaymentAndInterest(
                        $addonAmount,
                        $addon->Interest_Rate,
                        $addon->Term,
                        $addon->Repayment_Cycle,
                        $addon->Interest_Cycle
                    );
                    $totalpayment = $interestDetails['total_repayment'];
                    $interestCalculated = $interestDetails['total_interest'];
                } elseif ($addon->Interest_Calculation_Method === 'Amortization') {
                    $interestDetails = LoanSchedule::calculateAmortizedTotalRepaymentAndInterest(
                        $addonAmount,
                        $addon->Interest_Rate,
                        $addon->Term,
                        $addon->Repayment_Cycle,
                        $addon->Interest_Cycle
                    );
                    $totalpayment = $interestDetails['total_repayment'];
                    $interestCalculated = $interestDetails['total_interest'];
                }
            }

            $downPayment = round($addonAmount * ($addon->Downpayment_Percentage / 100));
        }

        return [$addonAmount, $interestCalculated, $downPayment, $totalpayment];
    }

    public function canBeRestructured(): bool
    {
        if ($this->Can_Restructure || is_null($this->latestOutstandingPayment)) {
            return false;
        }

        return $this->Number_Of_Restructures < 3 &&
            round($this->latestOutstandingPayment->payment_due_date->diffInDays(now()), 2) > 7;
    }

    public function getAmountWrittenOff()
    {
        $amount =  LoanRepayment::where('Credit_Account_Status', self::ACCOUNT_STATUS_WRITTEN_OFF)->get('Current_Balance_Amount', '')->toArray();

        return $amount[0];
    }

    public function getOverdueAmount(string|\Illuminate\Support\Carbon $asAt = null)
    {
        if (is_null($asAt)) {
            $asAt = now();
        }

        if ($this->Credit_Payment_Frequency === CreditPaymentFrequency::Monthly->name && $this->Number_of_Payments === 1) {
            // We are dealing with a monthly loan that has only one expected payment
            return $this->schedule->sum('total_outstanding');
        }

        return $this->schedule->where('payment_due_date', '<', $asAt)->sum('total_outstanding');
    }

    public function daysToExpiry(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (now()->isAfter($this->Maturity_Date)) {
                    return 0;
                }

                return round(now()->diffInDays($this->Maturity_Date, true));
            },
        );
    }

    /**
     * @deprecated Use $this->account_number instead
     */
    public function formattedId(): Attribute
    {
        return Attribute::make(
            get: fn() => 'L' . str($this->id)->padLeft(5, '0')
        );
    }

    public function accountNumber(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->id < ********) {
                    return 'L1' . str($this->id)->padLeft(7, '0');
                }

                return 'L' . $this->id;
            }
        );
    }
}
