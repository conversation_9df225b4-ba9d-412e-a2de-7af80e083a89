<?php

namespace App\Models;

use App\Enums\LoanAccountType;
use App\Models\LoanRepayment;
use App\Models\LoanDisbursement;
use App\Models\Scopes\BarnScope;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use App\Models\Transactables\SavingsDeposit;
use App\Models\Transactables\SavingsWithdraw;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Arr;

class Customer extends Model
{
    use HasFactory, SoftDeletes, Notifiable;

    protected $fillable = [
        'First_Name',
        'Last_Name',
        'Other_Name',
        'Gender',
        'Marital_Status',
        'Date_of_Birth',
        'ID_Type',
        'ID_Number',
        'Telephone_Number',
        'Email_Address',
        'Classification',
        'options',
    ];

    protected function casts(): array
    {
        return [
            'Date_of_Birth' => 'datetime',
            'options' => 'array',
        ];
    }

    // Create savings account when a customer is created
    protected static function booted()
    {
        static::addGlobalScope(new BarnScope); // You can barn a customer.
        // static::created(function ($customer) {
        //     if (Auth::check()) {
        //         $savingsProduct = SavingsProduct::where('partner_id', Auth::user()->partner_id)
        //             ->first();
        //         $savingsAccount = new SavingsAccount();
        //         $savingsAccount->customer_id = $customer->id;
        //         $savingsAccount->partner_id = Auth::user()->partner_id;
        //         $savingsAccount->savings_product_id = $savingsProduct->id;
        //         $savingsAccount->save();
        //     } else {
        //     }
        // });
    }

    public static function rules($customer = null)
    {
        $customer_email_rule = [];
        if ($customer) {
            $customer_email_rule = ['email_address' => 'nullable|string|max:100|unique:customers,Email_Address,' . $customer->id];
        } else {
            $customer_email_rule = ['email_address' => 'required|string|max:100|unique:customers,Email_Address'];
        }
        return [
            "first_name" => "required|string|max:100",
            "last_name" => "required|string|max:100",
            "other_name" => "nullable|string|max:100",
            "gender" => "required|string|max:100|in:Male,Female",
            "marital_status" => "sometimes|string|max:100|in:Single (never married),Married,Divorced,Widowed,Separated,Annulled,Cohabitating,Other",
            "date_of_birth" => "required|date",
            "id_type" => "required|string", // TODO: Add validation
            "id_number" => "required|string|max:100",
            "classification" => "required|string|max:100|in:Individual,Non-Indvidual",
            "telephone_number" => "required|string|max:15"
        ] + $customer_email_rule;
    }

    public function getNameAttribute()
    {
        return $this->First_Name . ' ' . $this->Last_Name;
    }

    public function savings_account()
    {
        return $this->hasMany(SavingsAccount::class, 'customer_id');
    }

    public function savingPreference(): HasOne
    {
        return $this->hasOne(CustomerSavingsPreferences::class, 'Customer_ID');
    }

    public function savingApplication()
    {
        return $this->hasOne(SavingsApplication::class, 'Customer_ID');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'Telephone_Number', 'Telephone_Number');
    }

    public function loans(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Loan::class, 'Customer_ID');
    }

    public function activeLoans(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->loans()
            ->whereNotIn('Credit_Account_Status', [
                LoanAccountType::PaidOff->value,
                LoanAccountType::WrittenOff->value,
            ])->latest();
    }

    public function unpaidLoan()
    {
        return $this
            ->loans()
            ->whereHas('latestOutstandingPayment');
    }

    public function loan_disbursements()
    {
        return $this->hasMany(LoanDisbursement::class, 'customer_id');
    }

    public function loan_repayments()
    {
        return $this->hasMany(LoanRepayment::class, 'Customer_ID');
    }

    public function customerAssets()
    {
        return $this->hasMany(CustomerAsset::class, 'Customer_ID');
    }

    public function journal_entries()
    {
        return $this->hasMany(JournalEntry::class, 'customer_id');
    }

    public function deposits()
    {
        return $this->hasMany(SavingsDeposit::class);
    }
    public function loanApplications()
    {
        return $this->hasMany(LoanApplication::class, 'Customer_ID');
    }

    public function withdrawals()
    {
        return $this->hasMany(SavingsWithdraw::class);
    }

    public function black_listed_loan(): HasOne
    {
        return $this->hasOne(BlackListedClient::class, 'Customer_ID');
    }

    public function validations()
    {
        return $this->morphMany(Validation::class, 'validateable');
    }

    public function barn($reason = null)
    {
        $this->IS_Barned = true;
        $this->Barning_Reason = $reason;
        $this->save();
    }

    public function unbarn()
    {
        $this->IS_Barned = false;
        $this->Barning_Reason = null; // Todo MA. Do this?
        $this->save();
    }

    public function fullName(): Attribute
    {
        return Attribute::make(
            get: function () {
                $fullName = $this->Last_Name . ' ' . $this->First_Name;

                if (! is_null($this->Other_Name)) {
                    return  $fullName .= " {$this->Other_Name}";
                }

                return $fullName;
            }
        );
    }

    public function routeNotificationForSms()
    {
        return $this->Telephone_Number; // Return the phone number for SMS notifications
    }

    public function optionalSavingsAccountNumber(): Attribute
    {
        return Attribute::make(
            get: function () {
                return data_get($this->options, 'savingsaccount.accountnumber');
            }
        );
    }

    /**
     * This is an optional saving account kept in the options field of the customer record.
     * It is only required for responses to MTN requests.
     *
     * @return bool
     */
    public function hasOptionalSavingsAccount(): bool
    {
        return ! empty($this->optional_savings_account_number);
    }

    public function doesNotHaveOptionalSavingsAccount(): bool
    {
        return $this->hasOptionalSavingsAccount() === false;
    }

    public function getCreditScores(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CreditScore::class, 'customerId')
            ->orderBy('created_at', 'desc');
    }

    public function accountNumber(): Attribute
    {
        return Attribute::make(
            get: function () {
                $savingAccountNumber = '1';

                if (str($this->id)->length() < 10) {
                    $savingAccountNumber .= str($this->id)->padLeft(10, '0');
                } else {
                    $savingAccountNumber = $this->id;
                }

                return $savingAccountNumber;
            }
        );
    }

    public function loanApplicationAccountNumber(): Attribute
    {
        return Attribute::make(
            get: function () {
                return data_get($this->options, 'loanaccounts.loanaccount.accountnumber');
            }
        );
    }

    public function hasActiveLoanApplicationWithMtn(): bool
    {
        return $this->hasOptionalSavingsAccount() && ! empty($this->loan_application_account_number);
    }

    public function blacklistedByPartners()
    {
        return $this->belongsToMany(Partner::class, 'blacklisted_customers')
            ->withPivot('reason')
            ->withTimestamps();
    }
    public function isBlacklistedByPartner($partnerId)
    {
        return $this->blacklistedByPartners()->where('partner_id', $partnerId)->exists();
    }
}
