<?php

namespace App\Actions;

use App\Models\LoanApplication;
use App\Models\Partner;
use App\Models\Transaction;

class CreateTestTransactionAction
{
  public function execute($amount, $phone, $partnerId)
  {
    $partner = Partner::query()->findOrFail($partnerId);
    //dd(LoanApplication::query()->where('partner_id', $partner->id)->latest()->first()?->id);
    if (! $partner->ova_setting) {
      throw new \Exception('OVA settings not found');
    }

    return Transaction::query()->Create(
        [
            'Partner_ID' => $partner->id,
            'Type' => 'Test',
            'Amount' => $amount,
            'Status' => 'Testing',
            'Telephone_Number' => $phone,
            'TXN_ID' => Transaction::generateID()->toString(),
            'Loan_Application_ID' => LoanApplication::query()->where('partner_id', $partner->id)->latest()->first()?->id
        ]
    );
  }
}
