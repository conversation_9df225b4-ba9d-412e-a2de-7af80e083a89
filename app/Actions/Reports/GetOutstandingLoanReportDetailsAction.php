<?php

namespace App\Actions\Reports;

use App\Enums\LoanAccountType;
use App\Models\Loan;
use App\Models\Customer;
use App\Models\LoanPenalty;
use Illuminate\Support\Arr;
use App\Models\JournalEntry;
use App\Models\LoanSchedule;
use Illuminate\Support\Carbon;
use App\Models\LoanDisbursement;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;

class GetOutstandingLoanReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $perPage = 0;
    protected bool $withWrittenOffLoans = false;

    public function execute()
    {
        $query = Loan::query()
            ->with('customer')
            ->whereNotIn('Credit_Account_Status', $this->getExcludedCreditAccountStatuses())
            ->withSum('schedule', 'principal_remaining')
            ->withSum('schedule', 'interest_remaining')
            ->withSum('schedule', 'total_outstanding')
            ->addSelect([
                'total_past_due' => LoanSchedule::query()
                    ->selectRaw('sum(total_outstanding)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->whereDate('payment_due_date', '<', $this->endDate)
                    ->limit(1),
                'total_pending_due' => LoanSchedule::query()
                    ->selectRaw('sum(total_outstanding)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->whereDate('payment_due_date', '>', $this->endDate)
                    ->limit(1),
                'penalty_amount' => LoanPenalty::query()
                    ->selectRaw('sum(amount_to_pay) - sum(amount)')
                    ->whereColumn('loan_id', 'loans.id')
                    ->whereDate('created_at', '<', Carbon::parse($this->endDate)->endOfDay()->toDateTimeString())
                    ->limit(1),
            ]);

        $query->orderBy('Credit_Account_Date', 'desc');

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }

        return $query->get();
    }

    public function paginate($perPage = 100): self
    {
        $this->perPage = $perPage;

        return $this;
    }

    public function filters(array $details): self
    {
        $this->endDate = Arr::get($details, 'endDate', now()->toDateString());

        if (Carbon::parse($this->endDate)->isFuture() || empty($this->endDate)) {
            $this->endDate = now()->toDateString();
        }

        $this->withWrittenOffLoans = Arr::get($details, 'withWrittenOffLoans', false);

        return $this;
    }

    /**
     * @return array
     */
    public function getExcludedCreditAccountStatuses(): array
    {
        $statuses = [
            LoanAccountType::PaidOff->value,
        ];

        if ($this->withWrittenOffLoans) {
            return $statuses;
        }

        $statuses[] = LoanAccountType::WrittenOff->value;

        return $statuses;
    }
}
