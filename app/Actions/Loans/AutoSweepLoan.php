<?php

namespace App\Actions\Loans;

use App\Enums\LoanAccountType;
use App\Models\Loan;
use App\Models\LoanProductPenalties;
use App\Models\LoanSweep;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutoSweepLoan
{
    protected ?string $provider;

    /**
     * Execute auto sweep for a loan
     *
     * @param Loan $loan
     * @return bool
     * @throws Exception
     */
    public function execute(Loan $loan): bool
    {
        $loan->loadMissing(['customer', 'loan_product']);

        $provider = $loan->loan_product->Auto_Sweep_Provider;

        if (strtolower($provider) !== 'mtn') {
            Log::info("Auto sweep not supported for {$provider}");
            // todo: Add support for other providers. For now, we only support MTN.
            return false;
        }

        $loanProductPenalty = LoanProductPenalties::query()
            ->where('Loan_Product_ID', $loan->Loan_Product_ID)
            ->where('Recurring_Penalty_Interest_Period_Value', '>', 0)
            ->first();

        $startAutoSweepOn = $loan->Maturity_Date;

        // todo: Allow provider to specify rollover period so that we do not have to figure out this way.
        if (! empty($loanProductPenalty) && strtolower($loanProductPenalty->Recurring_Penalty_Interest_Period_Type) === 'daily') {
            // Add rollover period.
            $startAutoSweepOn->addDays($loanProductPenalty->Recurring_Penalty_Interest_Period_Value);
        }

        if (now()->isBefore($startAutoSweepOn)) {
            Log::info("Loan #{$loan->id} is not past maturity date or still within rollover period. Auto sweep will start on {$startAutoSweepOn->toDateString()}");

            return false;
        }

        return DB::transaction(function () use ($loan) {
            $loan->loadMissing(['schedule']);

            $outstandingBalance = $loan->totalOutstandingBalance();
            // Create a transaction record for the auto sweep
            $transactionRecord = [
                'Partner_ID' => $loan->Partner_ID,
                'Type' => Transaction::REPAYMENT,
                'Amount' => $outstandingBalance,
                'Status' => 'Pending',
                'Telephone_Number' => $loan->customer->Telephone_Number,
                'TXN_ID' => Transaction::generateID()->toString(),
                'Loan_ID' => $loan->id,
                'Loan_Application_ID' => $loan->Loan_Application_ID,
                'Provider_TXN_ID' => null,
                'Payment_Reference' => null,
            ];

            $transaction = new Transaction($transactionRecord);
            $transaction->save();

            // Initiate auto collect through payment service
            try {
                $loanDetails = [];

                if (empty($loan->Auto_Sweep_Started)) {
                    $loanDetails['Auto_Sweep_Started'] = now();
                }

                $api = (new PaymentServiceManager($transaction))->paymentService;
                $responseDetails = $api->autoCollect($transaction);

                $loan->refresh();

                if ($loan->Credit_Account_Status === LoanAccountType::PaidOff->value) {
                    $loanDetails['Auto_Sweep_Ended'] = now()->toDateTimeString();
                }

                // Update Auto_Sweep_Started field
                if (! empty($loanDetails)) {
                    $loan->update($loanDetails);
                }

                // The auto collect will update the transaction amount
                $transaction->refresh();

                // Create LoanSweep record
                LoanSweep::query()->create([
                    'partner_id' => $loan->Partner_ID,
                    'loan_id' => $loan->id,
                    'customer_id' => $loan->Customer_ID,
                    'amount' => $transaction->Amount,
                    'transaction_date' => now(),
                    'provider_transaction_id' => $transaction->Payment_Reference,
                    'loan_product_code' => $loan->loan_product->Code,
                ]);

                Log::info('Auto Collect Response Details: ' . json_encode($responseDetails));

                return true;
            } catch (Exception $e) {
                Log::error("Auto sweep failed for loan #{$loan->id}: " . $e->getMessage());
                return false;
            }
        });
    }

    public function useProvider(string $provider): self
    {
        $this->provider = $provider;

        return $this;
    }
}
