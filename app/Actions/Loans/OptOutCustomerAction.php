<?php

namespace App\Actions\Loans;

use App\Models\Customer;
use App\Models\Partner;
use App\Notifications\SmsNotification;
use App\Services\MtnApiService;
use Exception;
use Illuminate\Support\Facades\DB;

class OptOutCustomerAction
{
    /**
     * @throws Exception
     */
    public function execute(Customer $customer, string $partnerCode): bool
    {
        $env = app()->isProduction() ? 'production' : 'test';
        DB::beginTransaction();
        try {
            $api = new MtnApiService($env);
            $result = $api->unlinkCustomer($customer);

            if (! $result) {
                throw new Exception('Opt-out failed.');
            }

            $options = $customer->options;

            unset($options['savingsaccount']);
            unset($options['loanaccounts']);

            $options['opt_out_at'] = now()->toDateTimeString();
            $customer->options = $options;
            $customer->save();

            $message = 'You have successfully opted out of Weekend Agent Loan.';
            $partner = Partner::query()->firstWhere('Identification_Code', $partnerCode);

            if ($partner) {
                $customer->notify(new SmsNotification($message, $customer->Telephone_Number, $customer->id, $partner->id));
            }

            DB::commit();
            return true;
        } catch (\Throwable $th) {
            DB::rollBack();
            return false;
        }
    }
}
